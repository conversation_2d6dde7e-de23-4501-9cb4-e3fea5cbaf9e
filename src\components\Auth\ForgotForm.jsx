"use client";
import { useContext, useState } from "react";
import CustomTitle from "../Common/CustomTitle";
import Link from "next/link";
import GlobalForm from "../Common/Custom-Form";
// import { useRouter } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import toast from "react-hot-toast";
import * as Yup from "yup";
import authStorage from "@/utils/API/AuthStorage";
import { valContext } from "../context/ValContext";
import { forgotPasswordSend } from "@/app/action";
import useApiRequest from "../helper/hook/useApiRequest";
import FullScreenLoader from "../Loader/FullScreenLoader";
import { safeToast } from "@/utils/safeToast";

import WebAppLogo from "../Common/WebAppLogo";

const ForgotForm = ({ setActiveForm }) => {
  const [isLoading, setIsLoading] = useState(false);
  const api = useApiRequest(false);
  const { forgotEmail, setForgotEmail } = useContext(valContext);
  const router = useRouter();
  const onSendOTPSubmit = async (e, actions) => {
    setIsLoading(true);
    const payload = e;
    api.sendRequest(
      forgotPasswordSend,
      (res) => {
        // console.log(res)
        authStorage?.setAuthDetails(res?.token);
        localStorage?.setItem("forgotemail", e?.email);
        setForgotEmail(e?.email);
        authStorage.setAuthDetails(res?.token);
        safeToast.success("OTP has been sent. Check your inbox.");
        actions?.setSubmitting(false);

        router.push("/send-otp?q=forgot-password");
      },
      payload,
      "",
      (err) => {
        safeToast.error(err?.message);
        setIsLoading(false);
      }
    );
  };

  return (
    <>
      {isLoading && <FullScreenLoader />}
      <WebAppLogo className="lg:tw-hidden" />
      <CustomTitle
        name="Forgot Password?"
        description="Enter registered email id to reset your password."
        textClassName="lg:!tw-text-incenti-24 !tw-text-3xl"
        className="tw-my-4 lg:tw-m-0 tw-text-3xl tw-text-primary-black tw-font-bold tw-text-center"
      />

      <GlobalForm
        fields={[{ name: "email", label: "Email", type: "email" }]}
        initialValues={{ email: forgotEmail }}
        validationSchema={{
          email: Yup.string()
            .trim()
            .email("Invalid email")
            .required("Email is required"),
          // .min(8, "Password must be at least 8 characters long")
          // .matches(
          //     /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&._-])[^\s]*$/,
          //     "(A-Z, a-z, 0-9, @$!%*?&._-), at least 1 of each & no spaces."
          // ),
        }}
        onSubmit={(e, actions) => onSendOTPSubmit(e, actions)}
        submitButtonText="Send OTP"
        // loading={api.isLoading}
        // extraButton={<>
        //     <div className="tw-w-full tw-flex tw-justify-center tw-text-center">
        //         <Link
        //             href="/forgot-password"
        //             className="tw-text-sm tw-text-center tw-text-primary-black hover:tw-underline tw-cursor-pointer"
        //             onClick={() => setActiveForm("forgot")}
        //         >
        //             Forgot password?
        //         </Link>
        //     </div></>}
      />

      {/* Sign Up Link */}
      <div className="tw-text-sm tw-text-primary-black tw-flex tw-font-bold">
        Back to{" "}
        <Link
          onClick={() => {
            setForgotEmail("");
            authStorage.deleteAuthDetails();
          }}
          href={"/login"}
          className="tw-text-primary-purple tw-font-bold hover:tw-underline"
        >
          &nbsp;Sign in
        </Link>
      </div>
    </>
  );
};

export default ForgotForm;
