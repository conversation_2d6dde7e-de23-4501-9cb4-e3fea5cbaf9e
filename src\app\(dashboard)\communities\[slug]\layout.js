import SingleCommunity from "@/components/Communities/SingleCommunity";
import { getServerOrigin } from "@/utils/ServerFunctions";

const SingleCommunityLayout = async ({ tabs, params }) => {
    const { slug } = await params;
    const origin = await getServerOrigin();
    return (
        <div>
            {/* {tabs} */}
            <SingleCommunity origin={origin} slug={slug} >
                {tabs}
            </SingleCommunity>
        </div>
    );
}

export default SingleCommunityLayout;