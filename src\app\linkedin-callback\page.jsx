"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import axios from "axios";
import toast from "react-hot-toast";
import { linkedinLogin } from "../action";
import authStorage from "@/utils/API/AuthStorage";
import setCookie from "@/utils/Cookies";

import { safeToast } from "@/utils/safeToast";
import useApiRequest from "@/components/helper/hook/useApiRequest";

export default function LinkedInCallback() {
  const params = useSearchParams();
  const router = useRouter();
  const api = useApiRequest();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const code = params.get("code");

    if (code) {
      toast.loading("Connecting to LinkedIn...");
      axios
        .post("/api/linkedin-auth", { code })
        .then((res) => {
          const data = res.data;
          toast.dismiss();

          if (data.access_token) {
            toast.loading("Logging you in...");
            api.sendRequest(
              linkedinLogin,
              (res) => {
                toast.dismiss();
                authStorage.setAuthDetails(res?.token);
                authStorage.setProfileDetails(res.data);
                setCookie("UID", res?.data?.uid, 365);

                setIsProcessing(false);

                if (res?.data?.isNewUser) {
                  router.push("/create-profile");
                  safeToast.success(
                    "Your email is verified. Please complete your registration."
                  );
                  sessionStorage.setItem("user", JSON.stringify(res.data));
                } else {
                  toast.success("Welcome");
                  router.push("/");
                }
              },
              { accessToken: data.access_token }
            );
          } else {
            toast.error("LinkedIn login failed: Invalid token.");
            localStorage.removeItem("isLinkedinState");
            router.push("/login");
            setIsProcessing(false);
          }
        })
        .catch((err) => {
          toast.dismiss();
          console.error("LinkedIn login error:", err);
          toast.error("LinkedIn login failed. Please try again.");
          localStorage.removeItem("isLinkedinState");
          router.push("/login");
          setIsProcessing(false);
        });
    } else {
      toast.error("Missing LinkedIn code in URL.");
      localStorage.removeItem("isLinkedinState");
      router.push("/login");
      setIsProcessing(false);
    }
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen bg-white">
      <div className="text-center space-y-3">
        <div className="animate-spin border-4 border-gray-300 border-t-blue-500 rounded-full w-12 h-12 mx-auto"></div>
        <p className="text-gray-700 text-lg font-medium">
          {isProcessing
            ? "Logging you in with LinkedIn..."
            : "Something went wrong."}
        </p>
      </div>
    </div>
  );
}
