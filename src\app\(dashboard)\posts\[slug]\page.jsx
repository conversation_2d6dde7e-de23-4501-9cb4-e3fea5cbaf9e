import { getOnePost } from "@/app/action";
import SinglePost from "@/components/HomePage/SinglePost";
import { getServerOrigin } from "@/utils/ServerFunctions";

// ✅ Fetch post data for metadata
// export async function generateMetadata({ params }) {
//   const { slug } = await params;
//   const origin = await getServerOrigin();

//   try {
//     const post = await getOnePost({
//       id: slug?.toString(),
//     });

//     const coverImage = post?.data?.media?.filter(
//       (ele) => ele?.isCoverImage
//     )?.[0]?.link;
//     console.log(coverImage);
//     // postData?.media?.length > 0
//     //   ? postData?.media?.filter((ele) => ele?.isCoverImage)
//     //   : null;
//     const title = post?.data?.title;
//     return {
//       title,
//       openGraph: {
//         title,
//         url: `${origin}/posts/${slug}`,
//         type: "website",
//         images: [
//           {
//             url: coverImage,
//             width: 1200,
//             height: 630,
//             alt: "Cover Image",
//           },
//         ],
//       },
//       twitter: {
//         card: "summary_large_image",
//         title,
//         images: [coverImage],
//       },
//     };
//   } catch (error) {
//     return {
//       title: "Post Not Found",
//       description: "",
//     };
//   }
// }

const SinglePostPage = async (props) => {
  const { slug } = await props?.params;
  const origin = await getServerOrigin();

  try {
    await getOnePost(slug);
  } catch (error) {
    // handle error if needed
  }

  return (
    <div>
      <SinglePost origin={origin} slug={slug} />
    </div>
  );
};

export default SinglePostPage;
