import { useContext, useEffect, useState } from "react";
import { <PERSON>, Heart } from "lucide-react";
import { valContext } from "../context/ValContext";
import { formatNumber } from "@/utils/function";
import { HeartFillOutIcon, HeartIcon } from "@/utils/icons";

const CustomSeeMore = ({
  story,
  action,
  isCreatedByCurrentUser,
  counts,
  viewHandler,
  likeHandler,
}) => {
  const { storyStats, setStoryStats } = useContext(valContext);
  const [storyCount, setStoryCount] = useState({});
  const storyId = story?.id;

  // ✅ Toggle like/dislike
  const handleLikeToggle = async () => {
    likeHandler(!storyCount?.isLikedByCurrentUser);
    setStoryCount((prev) => ({
      ...prev,
      isLikedByCurrentUser: !prev?.isLikedByCurrentUser,
      likeCount: prev?.isLikedByCurrentUser
        ? +prev?.likeCount - 1
        : +prev?.likeCount + 1,
    }));
    setTimeout(() => {
      setStoryStats((prev) => {
        return {
          ...prev,
          [storyId]: {
            ...prev[storyId],
            isLikedByCurrentUser: !prev[storyId]?.isLikedByCurrentUser,
            likeCount: prev[storyId]?.isLikedByCurrentUser
              ? +prev[storyId]?.likeCount - 1
              : +prev[storyId]?.likeCount + 1,
          },
        };
      });
    }, 300);
  };

  useEffect(() => {
    if (storyStats[storyId]) {
      setStoryCount(storyStats[storyId]);
    } else {
      // Default data
      let data = {};

      if (isCreatedByCurrentUser) {
        data = {
          likeCount: +counts?.likeCount ?? 0,
          viewCount: +counts?.viewCount ?? 0,
          isLikedByCurrentUser: story?.isLikedByCurrentUser ?? false,
        };
      } else {
        data = {
          likeCount: +story?.likeCount ?? 0,
          isLikedByCurrentUser: story?.isLikedByCurrentUser ?? false,
        };
      }
      setStoryCount(data);
      setStoryStats((prev) => ({
        ...prev,
        [storyId]: data,
      }));
    }
  }, []);

  return isCreatedByCurrentUser ? (
    <>
      <div className="tw-py-3 tw-px-5 tw-rounded-full tw-mx-9 tw-flex tw-justify-between tw-items-center tw-mb-3 tw-font-semibold tw-bg-white/15  tw-text-white">
        {/* See More Button */}
        <button
          type="button"
          onClick={() => {
            action("pause");
            viewHandler();
            //   getStoryLikeUserList(story, action);
            // toggleMore("true");
          }}
          className="tw-flex tw-gap-2 tw-items-center"
        >
          <Eye /> <p className="">{formatNumber(storyCount?.viewCount ?? 0)}</p>
        </button>
        {/* Like Button */}
        <button
          type="button"
          className=""
          onClick={() => {
            handleLikeToggle(story);
            //   updateStoryLike(story);
            //   action("pause");
          }}
        >
          <div className="tw-flex tw-justify-center tw-gap-2 tw-items-center tw-py-2 tw-px-3 tw-bg-white/15 tw-text-white tw-rounded-full  ">
            {storyCount?.isLikedByCurrentUser ? (
              <HeartFillOutIcon size={20} />
            ) : (
              <HeartIcon size={20} stroke="#fff" />
            )}
            <p className="tw-font-semibold tw-text-lg">
              {formatNumber(storyCount?.likeCount ?? 0)}
            </p>
          </div>
        </button>
      </div>
    </>
  ) : (
    <button
      type="button"
      className="tw-flex tw-justify-center tw-mb-3 tw-mx-auto"
      onClick={() => {
        //   updateStoryLike(story);
        //   action("pause");
        handleLikeToggle(story);
      }}
    >
      <div className="tw-flex tw-justify-center tw-gap-2 tw-items-center tw-p-3 tw-bg-white/15 tw-text-white tw-rounded-full  ">
        {storyCount?.isLikedByCurrentUser ? (
          <HeartFillOutIcon />
        ) : (
          <HeartIcon stroke="#fff" />
        )}
        <p className=""> {formatNumber(storyCount?.likeCount ?? 0)}</p>
      </div>
    </button>
  );
};

export default CustomSeeMore;
