"use client";
import { useContext } from "react";
import { CustomContainer } from "../Common/Custom-Display";
import { valContext } from "../context/ValContext";
import { usePathname } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";

const Search = ({ children }) => {
  const pathName = usePathname().split("/")[2];
  const router = useRouter();
  const {
    setTotalSearchRecords,
    totalSearchRecords,
    globalSearch,
    setSearchData,
    setSearchPagination,
  } = useContext(valContext);
  const searchSections = [
    {
      label: "Posts",
      value: "posts",
      className: "tw-w-[6rem] lg:tw-w-auto",
    },
    {
      label: "Projects",
      value: "projects",
      className: "tw-w-[7rem] lg:tw-w-auto",
    },
    {
      label: "Users",
      value: "users",
      className: "tw-w-[6rem] lg:tw-w-auto",
    },
    {
      label: "Communities",
      value: "communities",
      className: "tw-w-[9rem] lg:tw-w-auto",
    },
  ];

  return (
    <>
      <CustomContainer className="tw-py-4 tw-h-full">
        <div className="tw-flex tw-items-center tw-gap-3 tw-overflow-auto">
          {searchSections?.map((ele) => (
            <button
              type="button"
              onClick={() => {
                const basePath = "/search";
                const searchPath = ele.value === "posts" ? "" : `/${ele.value}`;
                if (ele?.value !== pathName) {
                  setTotalSearchRecords(0);
                  // setSearchData([]);
                  // setSearchPagination({
                  //   page: 1,
                  //   limit: 20,
                  //   total: 0,
                  // });
                }
                router.push(`${basePath + searchPath}`);
              }}
              key={ele.value}
              className={`tw-py-2.5 tw-px-5 tw-border    tw-text-medium tw-rounded-full ${
                ele.value === (pathName ?? "posts")
                  ? "tw-bg-primary-purple tw-border-primary-purple tw-text-white"
                  : "tw-text-secondary-text tw-border-secondary-text"
              }`}
            >
              <p
                className={`${
                  globalSearch &&
                  totalSearchRecords !== 0 &&
                  ele.value === (pathName ?? "posts")
                    ? ele?.className
                    : "tw-w-auto"
                }`}
              >
                {ele.label}{" "}
                {`${
                  globalSearch &&
                  totalSearchRecords !== 0 &&
                  ele.value === (pathName ?? "posts")
                    ? `(${totalSearchRecords})`
                    : ""
                }`}
              </p>
            </button>
          ))}
        </div>
        <div className="lg:tw-mt-4 tw-h-[83dvh] tw-overflow-y-auto">
          {children}
        </div>
      </CustomContainer>
    </>
  );
};

export default Search;
