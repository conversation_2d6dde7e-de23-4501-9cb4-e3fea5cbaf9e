import { Inter } from "next/font/google";
// import 'bootstrap/dist/css/bootstrap.min.css';
import "./globals.css";
import ValProvider from "@/components/context/ValContext";
import { Toaster } from "react-hot-toast"
import { TooltipProvider } from "@/components/ui/tooltip";
import { Suspense } from "react";
import TopProgressBar from "@/components/Loader/TopProgressBar";
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});


export const metadata = {
  title: "Floment ai",
  description: "Inspire Others by Unleashing your Own Growth Potential",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <link rel="manifest" href="/site.webmanifest" />
      <link rel="apple-touch-icon" href="apple-touch-icon.png" />
      <link rel="icon" type="image/x-icon" sizes="32x32" href="/favicon.ico" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="mask-icon" href="/favicon.svg" color="#6D11D2" />
      <body
        className={`${inter.className} antialiased`}
      >
        {/* <div className="tw-flex tw-min-h-screen tw-flex-col tw-justify-between"> */}
        <TopProgressBar
        />
        <ValProvider>
          <TooltipProvider delayDuration={70}>
            <Suspense fallback={<div>Loading...</div>}>
              {children}
            </Suspense>
          </TooltipProvider>
          <Toaster
            containerClassName="!tw-z-[999999]"
            toastOptions={{
              // Define default options
              className: '',
              duration: 1500,
              removeDelay: 1000,


              // Default options for Success
              success: {
                duration: 1500,
                style: {
                  background: '#10BE5B',
                  color: '#fff',
                  maxWidth: "900px",
                },
                iconTheme: {
                  primary: '#40cb7c',
                },

              },
              // Default options for error
              error: {
                duration: 1500,
                style: {
                  background: '#EF3B41',
                  color: '#fff',
                  maxWidth: "900px",
                  zIndex: "999999 !important"
                },
                iconTheme: {
                  primary: 'red',
                },
              },
            }}
          />
        </ValProvider>

        {/* </div> */}
      </body>
    </html>
  );
}
