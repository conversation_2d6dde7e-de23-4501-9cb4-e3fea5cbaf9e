"use client";

import { useEffect, useMemo, useState } from "react";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  addComments,
  followUser,
  getCommentList,
  getOnePost,
  reportPost,
  unFollowUser,
  updatePostLike,
  updatePostWishList,
} from "@/app/action";
import Image from "next/image";
import Empty from "../Common/Empty";
import CustomTitle from "../Common/CustomTitle";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import {
  addDataListToOrderedListItems,
  blurDataURL,
  formatNumber,
  removeTargetAttributeFromLink,
  RESPONSE_STATUS,
} from "@/utils/function";
import authStorage from "@/utils/API/AuthStorage";
import Loader from "../Loader/Loader";
import moment from "moment";
import toast from "react-hot-toast";
import {
  BookmarkFillOutIcon,
  BookmarkIcon,
  CloseModalIcon,
  HeartFillOutIcon,
  HeartIcon,
  HierarchyIcon,
  LeftArrowBackIcon,
  MessageIcon,
  ReportPostIcon,
  ShareIcon,
} from "@/utils/icons";
import ReportModal from "../Common/ReportModal";
import ShareModal from "../Common/ShareModal";
import { CustomContainer, HeadingTags } from "../Common/Custom-Display";
import CommentDrawer from "./CommentDrawer";
import { useRouter } from "nextjs-toploader/app";

import { safeToast } from "@/utils/safeToast";
import "quill/dist/quill.snow.css";
import UserAvatar from "../Common/UserAvatar";
import tempProjectImg from "../../../public/images/assets/default_image.png";
import tempCommunityImg from "../../../public/images/assets/group-community.jpg";
import PostDescription from "./PostDescription";
import FollowingAndFollowersModal from "../Profile/FollowingAndFollowersModal";
import noProjectImg from "../../../public/images/assets/not_folder.png";

const SinglePost = ({ slug, origin }) => {
  const [postData, setPostData] = useState(null);
  const [loginUser, setLoginUser] = useState(null);
  const [postState, setPostState] = useState({
    // isLiked: 0, // Convert to boolean
    // likedCount: +data?.likesCount,
    // commentCount: +data?.commentsCount ?? 0,
    // isWishListed: +data?.isBookMarked,
    // isFollowed: isNaN(+data?.User?.isFollowed) ? 0 : +data?.User?.isFollowed,
  });
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);
  // Comment States
  const [commentModal, setCommentModal] = useState(false);
  const [commentList, setCommentList] = useState([]);
  const [isCommentLoading, setIsCommentLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [type, setType] = useState({
    isOpen: false,
    type: null,
  });
  const [postId, setPostId] = useState(null);
  const api = useApiRequest();
  const singleOneAPI = useApiRequest(false);
  const router = useRouter();

  // console.log(postData);

  const HandlerIframeURL = (html) => {
    // Replace youtu.be URLs with embed URLs
    return html.replace(
      /https:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,
      "https://www.youtube.com/embed/$1"
    );
  };

  // console.log(postData);

  const processedDescription = useMemo(() => {
    return postData
      ? removeTargetAttributeFromLink(
          HandlerIframeURL(
            addDataListToOrderedListItems(postData?.description ?? "")
          )
        )
      : "";
  }, [postData?.description]);

  const updateLike = async (id) => {
    const res = await updatePostLike(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  const updateWishList = async (id) => {
    const res = await updatePostWishList(id);
    if (res?.status !== RESPONSE_STATUS.SUCCESS) {
      safeToast.error(res?.message);
    }
  };

  const getCommentData = async (id, setCommentList, setIsCommentLoading) => {
    try {
      const response = await getCommentList(id);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        setCommentList(response?.data?.data);
      } else {
        throw response;
      }
    } catch (error) {
      safeToast.error(error?.message);
    } finally {
      setIsCommentLoading(false);
    }
  };

  const onCommentSubmitHandler = async (values, resetForm) => {
    // console.log(data, "data");
    setIsLoading(true);
    const payload = {
      comment: values?.comment,
    };

    // console.log(ADD_COMMENT, payload);
    try {
      const res = await addComments({
        ...payload,
        id: postData?.id,
      });
      // console.log(res);
      if (res?.status === RESPONSE_STATUS.SUCCESS) {
        resetForm();
        setIsCommentLoading(true);
        setPostState((prev) => ({
          ...prev,
          commentCount: prev.commentCount + 1,
        }));
        setCommentList((prev) => [res?.data, ...prev]);
        setIsCommentLoading(false);
        // getCommentData(postData?.id, setCommentList, setIsCommentLoading);
      }
    } catch (err) {
      console.log(err);
      safeToast.error(err?.message);
    }
    setIsLoading(false);
  };

  const icons = [
    {
      id: 1,
      name: "Like",
      count: formatNumber(+postState?.likedCount) || 0,
      icon: postState?.isLiked ? (
        <HeartFillOutIcon size={22} />
      ) : (
        <HeartIcon stroke="#2D394A" size={22} />
      ),
      onClick: (ele) => {
        setPostState((prev) => ({
          ...prev,
          likedCount: prev.isLiked ? prev.likedCount - 1 : prev.likedCount + 1,
          isLiked: !prev.isLiked,
        }));
        // console.log(id, "Like");
        updateLike(ele?.id);
      },
    },
    {
      id: 2,
      count: formatNumber(+postState?.commentCount) || 0,
      icon: <MessageIcon stroke="#2D394A" size={22} />,
      onClick: (ele) => {
        // commentHandler(data?.id);
        setIsCommentLoading(true);
        setCommentModal(true);
        getCommentData(ele?.id, setCommentList, setIsCommentLoading);
      },
    },
    {
      id: 3,
      icon: postState?.isWishListed ? (
        <BookmarkFillOutIcon />
      ) : (
        <BookmarkIcon stroke="#2D394A" size={22} />
      ),
      onClick: (ele) => {
        setPostState((prev) => ({
          ...prev,
          isWishListed: !prev.isWishListed,
        }));
        updateWishList(ele?.id);
      },
    },
    {
      id: 4,
      icon: <ShareIcon stroke="#2D394A" size={22} />,
      onClick: () => {
        setIsShareOpen(true);
      },
    },
    {
      id: 5,
      icon: <ReportPostIcon size={22} />,
      onClick: (ele) => {
        // reportPostHandler(ele);
        setIsReported(true);
        setReportData(ele);
      },
    },
  ];

  // Follow and Unfollow Handler
  const followAndUnfollowHandler = async (userId, isFollow = true) => {
    let apiCall;
    try {
      if (isFollow) {
        apiCall = followUser;
      } else {
        apiCall = unFollowUser;
      }
      const response = await apiCall(userId);
      if (response?.status === RESPONSE_STATUS.SUCCESS) {
        safeToast.success(response?.message);
      } else {
        throw response;
      }
    } catch (error) {
      safeToast.error(error?.message);
    }
  };

  useEffect(() => {
    if (slug) {
      const userData = authStorage.getProfileDetails();
      setLoginUser(userData);
      api.sendRequest(
        getOnePost,
        (res) => {
          setPostData(res?.data);
          setPostState({
            isLiked: +res?.data?.isLiked, // Convert to boolean
            likedCount: +res?.data?.likesCount,
            commentCount: +res?.data?.commentsCount ?? 0,
            isWishListed: +res?.data?.isBookMarked,
            isFollowed: isNaN(+res?.data?.User?.isFollowed)
              ? 0
              : +res?.data?.User?.isFollowed,
          });
        },
        {
          id: slug?.toString(),
        }
      );
    }
  }, [slug]);
  return (
    <CustomContainer className="tw-py-4">
      <FollowingAndFollowersModal
        // setRefresh={setRefresh}
        postId={postId}
        modalType={type}
        setModalType={setType}
        loginUserData={loginUser}
        apiKey="likeUser"
        isSearchRequired={false}
      />
      {/* Comment Drawer */}{" "}
      <CommentDrawer
        isOpen={commentModal}
        setIsOpen={setCommentModal}
        setCommentList={setCommentList}
        isCommentLoading={isCommentLoading}
        isLoading={isLoading}
        onCommentSubmit={onCommentSubmitHandler}
        commentList={commentList}
        setPostState={setPostState}
      />
      <ShareModal
        open={isShareOpen}
        onClose={() => {
          setIsShareOpen(false);
        }}
        shareUrl={`${origin}/posts/${postData?.slug}`}
        title={`${postData?.title} \nCheckout this post:`}
        // title={`*${postData?.title}*\nCheckout this post:`}
      />
      <ReportModal
        isOpen={isReported}
        setIsOpen={setIsReported}
        isLoading={singleOneAPI?.isLoading}
        onConfirm={(reason) => {
          const payload = {
            reason,
            id: reportData?.id,
          };
          singleOneAPI.sendRequest(
            reportPost,
            (res) => {
              safeToast.success(res?.message);
              setIsReported(false);
              //   setRefresh((prev) => !prev);
              //   refetchData();
              setReportData(null);
            },
            payload
          );
        }}
      />
      {api.isLoading ? (
        <div className="tw-flex tw-justify-center tw-items-center">
          <Loader />
        </div>
      ) : postData ? (
        <div className="xl:tw-text-base tw-m-6 tw-relative">
          {/* <button
            type="button"
            onClick={() => {
              
              router.back();
            }}
            className="tw-mb-4"
          >
            <LeftArrowBackIcon />
          </button> */}
          {!api.isLoading && (
            <div>
              <div
                className={`tw-bg-white tw-fixed tw-w-full lg:tw-w-auto tw-z-10 tw-left-0 tw-right-0 tw-bottom-[3.5rem] tw-py-1.5 lg:tw-left-auto lg:tw-top-[6rem]  lg:tw-right-[6rem] tw-flex tw-justify-center lg:tw-justify-normal lg:tw-flex-col tw-items-center tw-gap-5`}
              >
                {/* <button
              className="tw-outline-none "
              type="button"
              onClick={() => {
                // setIsOpen(false);
                // router.push(path, { scroll: false });
              }}
            >
              <CloseModalIcon size={35} />
            </button> */}
                {icons
                  ?.filter((ele) =>
                    postData?.User?.id !== loginUser?.id ? ele : ele.id !== 5
                  )
                  ?.map((ele) => (
                    <button
                      onClick={() => {
                        ele?.onClick(postData);
                      }}
                      key={ele?.id}
                      type="button"
                      className="tw-bg-[#f7f0fd] tw-relative tw-outline-none tw-rounded-full tw-p-4 tw-flex tw-flex-col tw-items-center"
                    >
                      {/* <p className={`tw-relative `}>{ele?.icon}</p> */}
                      <p
                        className={`tw-relative ${
                          ele?.count > 0 && " -tw-top-1.5"
                        }`}
                      >
                        {ele?.icon}
                      </p>
                      {ele?.count && ele?.count > 0 && (
                        <p
                          onClick={(e) => {
                            e.stopPropagation();
                            if (ele?.name === "Like") {
                              setPostId(postData?.id);
                              setType({
                                isOpen: true,
                                type: "User",
                              });
                            }
                          }}
                          className="tw-text-sm tw-absolute tw-top-8"
                        >
                          {ele?.count ?? ""}
                        </p>
                      )}
                    </button>
                  ))}
              </div>
            </div>
          )}

          {/* tw-h-[30rem] */}
          <div className="tw-w-full lg:tw-w-[87%]  2xl:tw-col-span-8 xl:tw-col-span-7 lg:tw-col-span-8 md:tw-col-span-7 tw-col-span-12">
            <div className="tw-flex tw-flex-col 2xl:tw-gap-y-1 xl:tw-gap-y-2 lg:tw-gap-y-2.5 tw-gap-y-2">
              <div className="tw-flex tw-gap-10 tw-items-center tw-justify-between lg:tw-justify-normal">
                <Link
                  className="tw-inline-block"
                  href={
                    postData?.User?.id === loginUser?.id
                      ? "/profile"
                      : `/user/${postData?.User?.slug}`
                  }
                >
                  <div className="tw-flex tw-flex-row tw-items-center tw-gap-2">
                    <UserAvatar
                      imageUrl={postData?.User?.image}
                      userName={postData?.User?.firstName}
                      userNameClassName={"!tw-text-3xl"}
                    />
                    <div className="">
                      <h4 className="tw-text-base tw-font-semibold">{`${
                        postData?.User?.firstName
                      } ${
                        postData?.User?.lastName ? postData?.User?.lastName : ""
                      }`}</h4>

                      <p className="tw-text-primary-black tw-leading-[14px] tw-text-opacity-70 tw-text-[11px]">
                        {moment(postData?.createdAt)?.format("MMM DD, YYYY")}
                      </p>
                    </div>
                  </div>
                </Link>

                <div>
                  {loginUser?.id !== postData?.User?.id && (
                    <button
                      type="button"
                      onClick={() => {
                        setPostState((prev) => ({
                          ...prev,
                          isFollowed: prev?.isFollowed === 1 ? 0 : 1,
                        }));
                        followAndUnfollowHandler(
                          postData?.User?.id,
                          +!postState?.isFollowed
                        );
                      }}
                      className={`${
                        !postState?.isFollowed
                          ? "tw-bg-primary-purple tw-text-white"
                          : "tw-bg-transparent tw-border tw-border-[#787E89] tw-text-[#787E89]"
                      } tw-text-sm tw-py-2 tw-px-5  tw-rounded-full tw-font-medium`}
                    >
                      {+postState?.isFollowed ? "Following" : "Follow"}
                    </button>
                  )}
                </div>
              </div>
              {postData?.Project && (
                <div className="tw-mt-3 tw-flex tw-flex-col tw-gap-2 ">
                  <Link
                    href={`/projects/${
                      postData?.Project?.ParentProject?.slug ??
                      postData?.Project?.slug
                    }`}
                    className="tw-flex tw-gap-2 tw-items-start"
                  >
                    <div className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px]  tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center">
                      {
                        <Image
                          src={
                            postData?.Project?.ParentProject?.image ??
                            postData?.Project?.image ??
                            tempProjectImg
                          }
                          alt="Project"
                          fill
                          placeholder="blur"
                          blurDataURL={blurDataURL(20, 20)}
                          className="tw-object-cover tw-rounded-full"
                        />
                      }
                    </div>
                    <div>
                      <div className="tw-flex tw-gap-2 tw-items-center">
                        <p className="tw-font-semibold tw-text-[13px]">
                          {postData?.Project?.ParentProject?.name ??
                            postData?.Project?.name}
                          {/* {postData?.Project?.name} */}
                        </p>
                        <ChevronRight size={18} />
                      </div>
                    </div>
                  </Link>

                  {postData?.Project?.ParentProject && (
                    <Link
                      href={`/sub-project/${postData?.Project?.slug}`}
                      className="tw-flex tw-gap-2 tw-items-center tw-relative tw-left-[1.8rem]"
                    >
                      <HierarchyIcon size={18} />
                      <p className="tw-font-semibold tw-text-[13px]">
                        {postData?.Project?.name}
                        {/* {postData?.Project?.name} */}
                      </p>
                    </Link>
                  )}
                </div>
              )}
              {postData?.Community && (
                <div className="tw-mt-3 tw-flex tw-flex-col tw-gap-2 ">
                  <Link
                    href={`/communities/${postData?.Community?.slug}`}
                    className="tw-flex tw-gap-2 tw-items-start"
                  >
                    <div className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px]  tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center">
                      <Image
                        src={postData?.Community?.image ?? tempCommunityImg}
                        alt="Community"
                        fill
                        placeholder="blur"
                        blurDataURL={blurDataURL(20, 20)}
                        className="tw-object-cover tw-rounded-full"
                      />
                    </div>
                    <div>
                      <p className="tw-font-semibold tw-text-[13px]">
                        {postData?.Community?.name}
                        {/* {postData?.Project?.name} */}
                      </p>
                    </div>
                  </Link>

                  {postData?.Project?.ParentProject && (
                    <Link
                      href={`/sub-project/${postData?.Project?.slug}`}
                      className="tw-flex tw-gap-2 tw-items-center tw-relative tw-left-[1.8rem]"
                    >
                      <HierarchyIcon size={18} />
                      <p className="tw-font-semibold tw-text-[13px]">
                        {postData?.Project?.name}
                        {/* {postData?.Project?.name} */}
                      </p>
                    </Link>
                  )}
                </div>
              )}
              <div className="tw-flex tw-justify-between tw-mt-5 tw-mb-3">
                {/* <div>
                  <CustomTitle htag={2} name={`${postData?.title}`} />
                </div> */}
                <HeadingTags
                  htag={1}
                  className="tw-text-incenti-20 sm:tw-text-3xl tw-font-bold tw-text-primary-black "
                >
                  {postData?.title}
                </HeadingTags>
              </div>
            </div>
          </div>
          <PostDescription
            description={processedDescription}
            media={postData?.media}
            coverImage={
              postData?.media?.length > 0
                ? postData?.media?.filter((ele) => ele?.isCoverImage)
                : null
            }
          />
          {/* <div
            id=""
            className="ql-editor tw-w-full lg:tw-w-[87%] tw-mb-20 lg:tw-mb-0 !tw-p-0"
          >
            <div
              className="xl:tw-text-base !tw-relative project-description "
              dangerouslySetInnerHTML={{
                __html: processedDescription,
              }}
            />
          </div> */}
        </div>
      ) : (
        <>
          <div className="tw-h-[80vh] tw-flex tw-justify-center tw-items-center">
            <Empty
              icon={<Image src={noProjectImg} width={261} height={248} />}
              label="Post not found!"
            />
          </div>
        </>
      )}
    </CustomContainer>
  );
};

export default SinglePost;
