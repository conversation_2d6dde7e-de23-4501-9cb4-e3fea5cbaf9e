"use client";
import { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import {
  AddProjectIcon,
  EditProfileIcon,
  NoMemberFound,
  PrivateProjectIcon,
  ShareIcon,
  ThreeDotMenuIcon,
} from "@/utils/icons";
import Image from "next/image";
import {
  apiGenerator,
  blurDataURL,
  otherProjectSetting,
} from "@/utils/function";
import authStorage from "@/utils/API/AuthStorage";
import {
  deleteProject,
  getProject,
  reportProject,
  updateProject,
} from "@/app/action";
import { useRouter } from "nextjs-toploader/app";
import toast from "react-hot-toast";
import useApiRequest from "../helper/hook/useApiRequest";
import ReportModal from "../Common/ReportModal";
import ShareModal from "../Common/ShareModal";
import MainProjectCard from "../ProjectPage/MainProjectCard";
import InfiniteScroll from "../Common/InfiniteScroll";
import ProjectSkeleton from "../Loader/ProjectSkeleton";
import { valContext } from "../context/ValContext";
import Empty from "../Common/Empty";
import { safeToast } from "@/utils/safeToast";

const SearchProject = ({ origin }) => {
  // const [dataList, setDataList] = useState([]);
  // const [pagination, setPagination] = useState({
  //   page: 1,
  //   limit: 20,
  //   total: 0,
  // });
  const [isLoading, setIsLoading] = useState(true);
  const {
    globalSearch,
    setTotalSearchRecords,
    searchData: dataList,
    setSearchData: setDataList,
    searchPagination: pagination,
    setSearchPagination: setPagination,
  } = useContext(valContext);
  const [refresh, setRefresh] = useState(false);
  const [userData, setUserData] = useState(null);
  const router = useRouter();
  const api = useApiRequest();
  const projectAPI = useApiRequest(false);
  const [isShareOpen, setIsShareOpen] = useState(false);
  const [shareData, setShareData] = useState(null);
  const [isReported, setIsReported] = useState(false);
  const [reportData, setReportData] = useState(null);

  const resetState = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Fetch Data
  const fetchProjects = useCallback(
    async (search) => {
      setIsLoading(true); // ✅ Set loading before API call

      let queryParams = {
        page: pagination.page,
        limit: pagination.limit,
      };

      if (search) {
        queryParams = {
          ...queryParams,
          searchQuery: search?.trim(),
        };
      }
      try {
        await api.sendRequest(
          getProject,
          (res) => {
            if (search) {
              setTotalSearchRecords(res?.data?.totalRecords);
            }
            if (pagination.page === 1) {
              setDataList(res?.data?.data);
            } else {
              setDataList((prev) => [...prev, ...res?.data?.data]);
            }
            setPagination((prev) => ({
              ...prev,
              total: res?.data?.totalRecords,
            }));
          },

          queryParams
        );
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        setIsLoading(false); // ✅ Set loading false after API response (success or error)
      }
    },
    [pagination.page, refresh]
  );

  // useEffect(() => {
  //   setUserData(authStorage.getProfileDetails());
  //   // if (globalSearch) {
  //   //   resetState();
  //   // }
  //   fetchProjects(search);
  // }, [pagination.page, refresh, search]);

  useEffect(() => {
    setUserData(authStorage.getProfileDetails());
    if (globalSearch) {
      fetchProjects(globalSearch);
    } else {
      fetchProjects();
    }
  }, [pagination.page, globalSearch, refresh]);

  useEffect(() => {
    resetState();
  }, []);

  // useEffect(() => {
  //   if (globalSearch !== search) {
  //     setSearch(globalSearch);
  //     resetState();
  //   }
  // }, [globalSearch]);
  return (
    <>
      <div>
        {/* Report Modal */}
        <ReportModal
          title="Why are you reporting this project?"
          isOpen={isReported}
          setIsOpen={setIsReported}
          isLoading={projectAPI?.isLoading}
          onConfirm={(reason) => {
            const payload = {
              reason,
              id: reportData?.id,
            };
            projectAPI.sendRequest(
              reportProject,
              (res) => {
                safeToast.success(res?.message);
                setIsReported(false);
                resetState();
                setRefresh((prev) => !prev);
                setReportData(null);
              },
              payload
            );
          }}
        />

        <ShareModal
          open={isShareOpen}
          onClose={() => {
            setIsShareOpen(false);
          }}
          shareUrl={`${origin}/projects/${shareData?.slug}`}
          title={`${shareData?.name} \nCheckout this project:`}
        />
        <div>
          <div className="tw-my-5 !tw-pb-6 tw-grid tw-grid-cols-2 lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7  tw-w-full ">
            {/* {isLoading && pagination.page === 1 && <ProjectSkeleton />} */}
            {dataList?.map((ele) => (
              <div
                key={ele?.id}
                className="tw-cursor-pointer"
                onClick={() => {
                  // if (ele?.isPrivate && ele?.ProjectMember?.length === 0) {
                  if (ele?.isPrivate) {
                    safeToast.success("This Project is Private");
                    return;
                  }
                  router.push(`/projects/${ele?.slug}`);
                }}
              >
                <MainProjectCard
                  ele={ele}
                  loginUserData={userData}
                  shareHandler={(ele) => {
                    setIsShareOpen(true);
                    setShareData(ele);
                  }}
                  reportHandler={(ele) => {
                    setIsReported(true);
                    setReportData(ele);
                  }}
                  isMenuVisible={!ele?.isPrivate}
                />
              </div>
            ))}
            <InfiniteScroll
              threshold={90}
              loadMoreFunction={() => {
                if (
                  Math.ceil(pagination.total / pagination.limit) >
                  pagination?.page
                ) {
                  setPagination((prev) => ({
                    ...prev,
                    page: prev?.page + 1,
                  }));
                }
              }}
              isLoading={isLoading}
              loadingComponent={<ProjectSkeleton />}
              timeout={10}
              // loadOff={loadOff}
            />
          </div>

          {!isLoading && dataList?.length === 0 && (
            <div className="tw-flex tw-justify-center tw-items-center tw-h-[70vh]">
              <Empty
                icon={<NoMemberFound size={50} />}
                label={"Search Result Not Found!"}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default SearchProject;
