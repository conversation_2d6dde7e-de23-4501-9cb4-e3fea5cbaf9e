"use client";

import UserAvatar from "@/components/Common/UserAvatar";
import {
  blurDataURL,
  FLOMENT_ID,
  removeTargetAttributeFromLink,
} from "@/utils/function";
import { PinIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import moment from "moment";
import CustomTitle from "@/components/Common/CustomTitle";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ShowMenuList from "@/components/Common/ShowMenuList";
import PostImageCarousel from "../PostImageCarousel";
import {
  EditProfileIcon,
  ThreeDotMenuIcon,
  HierarchyIcon,
} from "@/utils/icons";

const LaptopPostCard = ({
  data,
  postNavigation,
  path,
  postState,
  isUserLinkActive,
  icons,
  setPostId,
  setType,
  otherProjectSetting,
  blockerUser,
  setRefresh,
  refetchData,
  blockImg,
  tempProjectImg,
  tempCommunityImg,
  isBlockUser,
  getPostDetails,
  setEditData,
  hasEditAccessOnly,
  myPostSetting,
  CommunityId,
  allSettings,
  userData,
  className = "",
  router,
  description,
}) => {
  // console.log(truncateHtmlWithLineLimit(data?.description, { maxLines: 2 }));
  return (
    <>
      <div
        className={`tw-bg-[#F5F7F8]  tw-rounded-[20px] lg:tw-rounded-[30px] tw-cursor-pointer  tw-hidden lg:tw-flex tw-justify-between tw-items-center tw-w-full  tw-gap-5 tw-h-[18rem] tw-py-6 tw-text-inherit  ${
          data?.media?.length > 0 ? "tw-pr-5 tw-pl-7" : "tw-px-7"
        }  ${className}`}
        onClick={() => {
          postNavigation(data?.slug);
        }}
      >
        <div className="tw-w-full ">
          <div className="tw-flex tw-flex-col 2xl:tw-gap-y-1 xl:tw-gap-y-2 lg:tw-gap-y-2.5 tw-gap-y-2">
            <div className="tw-inline-block">
              {postState?.pinPost &&
                (path.includes("/profile") ||
                  path.includes("projects") ||
                  path.includes("sub-project")) && (
                  <div className="tw-flex tw-gap-2 tw-items-center tw-relative -tw-top-2">
                    <div>
                      <PinIcon stroke="#787E89" size={15} />
                    </div>
                    <div className="tw-text-sm tw-text-secondary-text ">
                      Pinned Post
                    </div>
                  </div>
                )}
              {/* User Details */}
              {/* userData?.id === data?.User?.id
                    ? `/profile`
                    : `/user/${data?.User?.id}` */}
              <div className="tw-inline-block">
                <div
                  onClick={(e) => {
                    e.stopPropagation();

                    if (isUserLinkActive) {
                      if (userData?.id === data?.User?.id) {
                        router.push(`/profile`);
                      } else {
                        router.push(`/user/${data?.User?.slug}`);
                      }
                    }
                  }}
                  className={`${
                    isUserLinkActive && "tw-cursor-pointer"
                  } tw-flex tw-flex-row tw-gap-2`}
                >
                  <UserAvatar
                    imageUrl={data?.User?.image}
                    userName={data?.User?.firstName}
                    userNameClassName={"!tw-text-3xl"}
                  />
                  <div className="">
                    <h4 className="tw-text-base tw-font-semibold">{`${
                      data?.User?.firstName
                    } ${data?.User?.lastName ? data?.User?.lastName : ""}`}</h4>
                    {data?.Community && (
                      <div className="tw-flex tw-flex-col tw-gap-2 ">
                        <Link
                          href={`/communities/${data?.Community?.slug}`}
                          className="tw-flex tw-gap-2 tw-items-start"
                        >
                          <div className="tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px]  tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center">
                            <Image
                              src={data?.Community?.image ?? tempCommunityImg}
                              alt="Community"
                              fill
                              placeholder="blur"
                              blurDataURL={blurDataURL(20, 20)}
                              className="tw-object-cover tw-rounded-full"
                            />
                          </div>
                          <div>
                            <p className="tw-font-semibold tw-text-[13px]">
                              {data?.Community?.name}
                              {/* {data?.Project?.name} */}
                            </p>
                          </div>
                        </Link>

                        {data?.Project?.ParentProject && (
                          <Link
                            href={`/sub-project/${data?.Project?.slug}`}
                            className="tw-flex tw-gap-2 tw-items-center tw-relative tw-left-[1.8rem]"
                          >
                            <HierarchyIcon size={18} />
                            <p className="tw-font-semibold tw-text-[13px]">
                              {data?.Project?.name}
                              {/* {postData?.Project?.name} */}
                            </p>
                          </Link>
                        )}
                      </div>
                    )}
                    {data?.Project && (
                      <div className="tw-flex tw-flex-row tw-gap-1 tw-items-center">
                        <div
                          className={`tw-relative md:tw-ws-[3.125rem] md:tw-hs-[3.125rem] tw-w-[18px] tw-h-[18px] ${
                            !data?.Project?.image &&
                            !data?.Project?.ParentProject?.image &&
                            "tw-bg-primary-purple"
                          } tw-rounded-full tw-overflow-hidden tw-cursor-pointer tw-flex tw-justify-center tw-items-center`}
                        >
                          {
                            <Image
                              src={
                                data?.Project?.ParentProject?.image ??
                                data?.Project?.image ??
                                tempProjectImg
                              }
                              alt="Project"
                              fill
                              placeholder="blur"
                              blurDataURL={blurDataURL(20, 20)}
                              className="tw-object-cover tw-rounded-full"
                            />
                          }
                        </div>
                        <p className="tw-font-semibold tw-text-[13px]">
                          {data?.Project?.ParentProject?.name ??
                            data?.Project?.name}
                        </p>
                      </div>
                    )}
                    <p className="tw-text-primary-black tw-text-opacity-70 tw-text-[13px]">
                      {moment(data?.createdAt)?.format("MMM DD, YYYY")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="tw-flex tw-justify-between">
              <div>
                <CustomTitle htag={2} name={`${data?.title}`} />
              </div>
            </div>
            {description && (
              <div className="tw-flex tw-justify-between tw-gap-0.5 tw-items-center">
                <div
                  id="post-description"
                  // ${data?.media?.length > 0 ? "tw-max-w-[27rem]": "tw-max-w-[50rem]"}
                  className={`ql-editor !tw-p-0 tw-text-sm tw-line-clamp-1 tw-w-full  tw-break-all !tw-overflow-hidden`}
                  dangerouslySetInnerHTML={{
                    __html: description,
                  }}
                />

                {/* <span
                  onClick={(e) => {}}
                  className="tw-text-incenti-purple tw-font-semibold tw-cursor-pointer"
                >
                  see more
                </span> */}
              </div>
            )}
            <div className="tw-flex tw-justify-between tw-items-center tw-mt-4">
              <div className="tw-flex tw-gap-2 tw-text-light-gray-700 xl:tw-text-base lg:tw-text-sm tw-text-12px">
                <div className="tw-flex tw-gap-x-5 ">
                  {icons
                    ?.filter((ele) =>
                      // +data?.User?.id === +FLOMENT_ID
                      data?.isCopyToEdit ? ele : ele?.name !== "Copy to Edit"
                    )
                    ?.map((icon, index) => (
                      <div key={index}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  icon.onClick(data?.id);
                                }}
                                className="tw-flex tw-gap-1 tw-items-center"
                              >
                                {icon?.icon}
                                {icon?.count && (
                                  <span
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (icon?.name === "Like") {
                                        // console.log("hello");
                                        setPostId(data?.id);
                                        setType({
                                          isOpen: true,
                                          type: "User",
                                        });
                                      }
                                    }}
                                    className=""
                                  >
                                    {icon?.count}
                                  </span>
                                )}
                              </button>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>{icon?.name}</TooltipContent>
                        </Tooltip>
                      </div>
                    ))}
                </div>
              </div>
              {/* <TooltipProvider delayDuration={0}> */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <ShowMenuList
                    align="center"
                    data={data}
                    menuList={
                      userData?.id === data?.User?.id || allSettings
                        ? CommunityId
                          ? myPostSetting?.filter(
                              (ele) =>
                                ele.label === "Edit" || ele.label === "Delete"
                            )
                          : myPostSetting
                        : hasEditAccessOnly
                        ? [
                            {
                              label: "Edit",
                              className: "",
                              icon: (
                                <EditProfileIcon size={17} stroke="#2D394A" />
                              ),
                              onClick: (ele) => {
                                getPostDetails(ele?.id, setEditData);
                              },
                            },

                            ...otherProjectSetting,
                          ]
                        : isBlockUser
                        ? [
                            ...otherProjectSetting,
                            {
                              label: "Block User",
                              className: "tw-text-[#EF3B41]",
                              // icon: <LogOut stroke="#EF3B41" size={17} />,
                              icon: (
                                <div>
                                  <Image
                                    src={blockImg}
                                    alt="block"
                                    height={16}
                                    width={16}
                                  />
                                </div>
                              ),
                              onClick: (ele) => {
                                // getPostDetails(ele?.id, setEditData);
                                // console.log(ele?.User?.id);
                                blockerUser(ele?.User?.id, () => {
                                  setRefresh((prev) => !prev);
                                  refetchData();
                                });
                              },
                            },
                          ]
                        : otherProjectSetting
                    }
                  >
                    <div className="tw-text-light-gray-700 xl:tw-text-base lg:tw-text-sm tw-text-12px tw-flex tw-justify-end tw-cursor-pointer tw-items-center">
                      <ThreeDotMenuIcon />
                    </div>
                  </ShowMenuList>
                </TooltipTrigger>
                <TooltipContent>Updated</TooltipContent>
              </Tooltip>
              {/* </TooltipProvider> */}
            </div>
          </div>
        </div>
        {/* Image Carousel */}
        {data?.media?.length > 0 && <PostImageCarousel imagArr={data?.media} />}
      </div>
    </>
  );
};

export default LaptopPostCard;
