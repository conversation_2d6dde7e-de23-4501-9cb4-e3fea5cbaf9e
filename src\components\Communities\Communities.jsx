"use client";
import { useCallback, useEffect, useState } from "react";
import { CustomContainer } from "../Common/Custom-Display";
import CommunitiesModal from "./CommunitiesModal";
import useApiRequest from "../helper/hook/useApiRequest";
import CustomTitle from "../Common/CustomTitle";
import { NoCommunityData, UserGroupIcon } from "@/utils/icons";
// import { useRouter } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import CustomButton from "../Common/Custom-Button";
import Image from "next/image";
import { blurDataURL } from "@/utils/function";
import { getCommunity, joinCommunity } from "@/app/action";
import InfiniteScroll from "../Common/InfiniteScroll";
import CommunitySkeleton from "../Loader/CommunitySkeleton";
import Empty from "../Common/Empty";
import CommunityCard from "./CommunityCard";
import toast from "react-hot-toast";
import TabSkeleton from "../Loader/TabSkeleton";

import { safeToast } from "@/utils/safeToast";
import { usePathname } from "next/navigation";

const Communities = ({ category }) => {
  const [openModal, setOpenModal] = useState(false);
  const [dataList, setDataList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
  });
  const [selectedCategory, setSelectedCategory] = useState(null);

  const [refresh, setRefresh] = useState(false);
  // const [editData, setEditData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const api = useApiRequest();
  const pathName = usePathname().split("/")[2];

  // console.log(pathName.split("/"));

  // Reset State
  const resetState = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  // Tabs Array
  const communitiesCategory = [
    {
      label: "Created",
      value: "created",
    },
    {
      label: "Joined",
      value: "joined",
    },

    {
      label: "Explore",
      value: "explore",
    },
  ];

  const errorMessage = {
    created: "No communities created yet.",
    joined: "No communities joined yet.",
    explore: "No communities available!",
  };

  const fetchProjects = useCallback(async () => {
    setIsLoading(true); // ✅ Set loading before API call

    let queryParams = {
      page: pagination.page,
      limit: pagination.limit,
    };

    // const userDetails = authStorage.getProfileDetails();
    if (selectedCategory === "joined") {
      queryParams = { ...queryParams, joined: 1 };
    } else if (selectedCategory === "created") {
      queryParams = { ...queryParams, created: 1 };
      //  if (Object.keys(userDetails)?.length > 0) {
      //    queryParams = { ...queryParams, UserId: userDetails?.id };
      //  }
    }

    try {
      await api.sendRequest(
        getCommunity,
        (res) => {
          if (pagination.page === 1) {
            setDataList(res?.data?.data);
          } else {
            setDataList((prev) => [...prev, ...res?.data?.data]);
          }
          setPagination((prev) => ({
            ...prev,
            total: res?.data?.totalRecords,
          }));
        },
        queryParams
      );
    } catch (error) {
      console.error("Error fetching projects:", error);
    } finally {
      setIsLoading(false); // ✅ Set loading false after API response (success or error)
    }
  }, [pagination.page, selectedCategory, refresh]);

  useEffect(() => {
    if (selectedCategory) {
      fetchProjects();
    }
  }, [pagination.page, selectedCategory, refresh]);

  useEffect(() => {
    setSelectedCategory(category);
  }, [category]);
  return (
    <>
      {/* Add Community */}
      <CommunitiesModal
        reFetchData={resetState}
        setRefresh={setRefresh}
        open={openModal}
        setOpen={setOpenModal}
        modalTitle={"Create Community"}
        modalSubmitButton={"Create"}
      />
      {/* Update Community */}
      {/* <CommunitiesModal
        reFetchData={resetState}
        setRefresh={setRefresh}
        editData={editData}
        setEditData={setEditData}
        modalTitle={"Edit Community"}
        modalSubmitButton={"Update"}
      /> */}
      <CustomContainer className="tw-py-4">
        {/* Title , Tabs and Add Project Button */}
        <div className="tw-w-full  tw-flex tw-justify-center lg:tw-justify-between tw-items-center">
          <CustomTitle
            className="w-full tw-hidden lg:tw-block"
            name="Communities"
          />
          {selectedCategory ? (
            <div className="tw-flex tw-justify-center tw-items-center tw-gap-2">
              {communitiesCategory?.map((ele) => (
                <div
                  onClick={() => {
                    setSelectedCategory(ele?.value);

                    router.push(`/communities/${ele?.value}`);
                    if (pathName !== ele?.value) {
                      resetState();
                    }
                  }}
                  key={ele?.label}
                  className={`tw-py-3 tw-cursor-pointer tw-rounded-full ${
                    ele?.value === selectedCategory
                      ? "tw-font-bold tw-bg-[#F6EFFE] tw-text-incenti-purple"
                      : "tw-bg-[#F5F7F8] tw-text-[#2D394A] tw-font-medium"
                  } tw-px-5`}
                >
                  {ele?.label}
                </div>
              ))}
            </div>
          ) : (
            <TabSkeleton />
          )}
          <div className="tw-fixed lg:tw-static tw-bottom-[8.5rem] tw-right-4">
            <CustomButton
              type="button"
              onClick={() => {
                setOpenModal(true);
                // console.log("here");
              }}
              className={`tw-px-[1.1rem] lg:tw-px-6`}
              count={8}
            >
              <div className="tw-flex tw-items-center tw-gap-1.5 tw-font-semibold">
                <UserGroupIcon stroke="#fff" className="" />
                <p className="tw-hidden lg:tw-block">Create</p>
              </div>
            </CustomButton>
          </div>
        </div>

        <div className="tw-my-5 !tw-pb-6 tw-grid  lg:tw-grid-cols-3 2xl:tw-grid-cols-4 tw-gap-7  tw-w-full ">
          {/* <CommunitySkeleton /> */}
          {dataList?.map((ele) => (
            <div
              key={ele?.id}
              onClick={(e) => {
                e.stopPropagation();
                // router.push(`/communities/${selectedCategory}/${ele?.id}`);

                router.push(`/communities/${ele?.slug}`);
              }}
              className="lg:tw-bg-[#F5F7F8] tw-cursor-pointer tw-rounded-3xl lg:tw-px-5 lg:tw-py-7 tw-grid tw-grid-cols-3 lg:tw-flex lg:tw-flex-col tw-items-center tw-gap-0 exs:tw-gap-2 lg:tw-gap-4 "
            >
              <CommunityCard
                ele={ele}
                isCreatedByMe={selectedCategory === "created"}
                joinHandler={async (id) => {
                  api.sendRequest(
                    joinCommunity,
                    (res) => {
                      //   console.log(res);
                      safeToast.success(res?.message);
                    },
                    {
                      id,
                    }
                  );
                }}
              />
            </div>
          ))}
          <InfiniteScroll
            threshold={90}
            loadMoreFunction={() => {
              if (
                Math.ceil(pagination.total / pagination.limit) > pagination.page
              ) {
                setPagination((prev) => ({
                  ...prev,
                  page: prev?.page + 1,
                }));
              }
            }}
            isLoading={isLoading}
            loadingComponent={<CommunitySkeleton />}
            timeout={10}
            // loadOff={loadOff}
          />
        </div>

        {dataList?.length === 0 && !isLoading && (
          <div className="tw-flex tw-justify-center tw-items-center tw-h-[50dvh]">
            <Empty
              className="tw-text-xl tw-font-medium tw-mt-2"
              label={errorMessage[selectedCategory]}
              icon={<NoCommunityData />}
            />
          </div>
        )}
      </CustomContainer>
    </>
  );
};

export default Communities;
