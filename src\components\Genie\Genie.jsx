"use client";
import { role, SOCKET_BASE_URL } from "@/utils/function";
import { useContext, useEffect, useRef, useState } from "react";
import dayjs from "dayjs";
import { io } from "socket.io-client";
import { CustomContainer } from "../Common/Custom-Display";
import { GenieChatIcon } from "@/utils/icons";
import ModifyBot from "./ModifyBot";
import { valContext } from "../context/ValContext";
import Link from "next/link";

let userChatCount = 0;
const Genie = () => {
  const [message, setMessage] = useState("");
  const [socket, setSocket] = useState(null);
  const [isMessageFetching, setIsMessageFetching] = useState(false);
  const [isDataFetching, setIsDataFetching] = useState(false);

  const { chatList, setChatList, firstTimeLoading, setFirstTimeLoading } =
    useContext(valContext);
  const textareaRef = useRef(null);
  const chatContainerRef = useRef(null);

  //   console.log(chatList);

  //  Chat Handler
  const chatHandler = () => {
    let messageData = message?.replace(/\n/g, "");
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto"; // reset height immediately after send
    }
    if (!messageData || messageData === "") return;
    // textareaRef.current.style.height = "10px";
    setMessage("");

    setIsMessageFetching(true);
    setIsDataFetching(true);
    ++userChatCount;
    let messagesList = [
      ...chatList,
      {
        role: role.user,
        content: messageData,
        timestamp: dayjs().toISOString(),
      },
    ];

    if (userChatCount % 5 === 0) {
      let subscribeMessage = (
        <div className="tw-max-w-[20.5rem]">
          <p>Help us fund our Startup Journey! 🚀</p>
          <p className="tw-my-2">
            Subscribe and be the first to unlock the premium features over our
            next update for just{" "}
            <span className="tw-font-semibold">$19.99/month</span>.
          </p>
          <Link
            href={"https://buy.stripe.com/3cscNCeCc2Ot04o288"}
            target="_blank"
            className="tw-text-primary-black tw-border tw-border-primary-black tw-rounded-full tw-text-xs tw-mt-2 tw-px-2 tw-py-1"
          >
            Subscribe Now
          </Link>
        </div>
      );
      messagesList = [
        ...messagesList,
        {
          role: role.assistant,
          content: subscribeMessage,
          timestamp: dayjs().toISOString(),
        },
      ];
    }

    setChatList(messagesList);

    // console.log("here");
    if (userChatCount % 5 !== 0) {
      socket.emit("sendMessage", {
        conversations: messagesList?.map((ele) => ({
          role: ele.role,
          content: ele?.content?.toString(),
        })),
      });
    } else {
      setIsMessageFetching(false);
      setIsDataFetching(false);
    }
  };
  // Socket Connection
  useEffect(() => {
    if (chatList?.length === 1) {
      setTimeout(() => {
        setFirstTimeLoading(false);
      }, 700);
    }

    const newSocket = io(SOCKET_BASE_URL, {
      // const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL, {
      autoConnect: false,
    });

    newSocket.connect();

    newSocket.on("connect", () => {
      // console.log("✅ Socket connected");
    });
    let chunkTimeout;
    newSocket.on("chatChunk", (data) => {
      // console.log(data, "data");

      if (Object.keys(data).length > 0) {
        setIsMessageFetching(false);
        // Clear existing timeout
        if (chunkTimeout) clearTimeout(chunkTimeout);

        // Process the chunk data here
        // Update your chat/message state
        let newText = data?.content;
        setChatList((prev) => {
          const lastIndex = prev.length - 1;
          const lastMessage = prev[lastIndex];

          if (lastMessage.role === role.user) {
            // Add new assistant message
            return [
              ...prev,
              {
                role: role.assistant,
                content: newText,
              },
            ];
          } else {
            // Append to last assistant message
            const updated = [...prev];
            updated[lastIndex] = {
              ...lastMessage,
              content: lastMessage.content + newText,
            };
            return updated;
          }
        });

        // Set new timeout for completion detection
        chunkTimeout = setTimeout(() => {
          setIsDataFetching(false);
        }, 500);
      }
    });

    newSocket.on("disconnect", () => {
      // console.log("❌ Socket disconnected");
    });

    newSocket.on("connect_error", (err) => {
      // console.error("⚠️ Socket connect error:", err.message);
      setIsDataFetching(false); // Reset loading state on error
    });

    setSocket(newSocket);

    return () => {
      if (chunkTimeout) clearTimeout(chunkTimeout);
      if (newSocket) {
        newSocket.removeAllListeners(); // Remove ALL listeners
        newSocket.disconnect();
      }
    };
  }, []);
  // Scrolling
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight, // Scroll to bottom
        behavior: "smooth",
      });
    }
  }, [chatList]);
  return (
    <>
      <CustomContainer className="tw-pt-4 tw-h-[82vh] lg:tw-h-[86vh] xxl:tw-h-[89vh]">
        <div className="tw-absolute tw-top-8 tw-left-[53%] tw-hidden lg:tw-block">
          <div className="tw-flex tw-gap-2 tw-items-center">
            <GenieChatIcon size={35} />{" "}
            <strong className="tw-text-2xl"> Flowie</strong>
          </div>
        </div>

        <ModifyBot
          message={message}
          setMessage={setMessage}
          firstTimeLoading={firstTimeLoading}
          chatList={chatList}
          isMessageFetching={isMessageFetching}
          chatHandler={chatHandler}
          isDataFetching={isDataFetching}
          role={role}
          textareaRef={textareaRef}
          chatContainerRef={chatContainerRef}
          userChatCount={userChatCount}
        />
      </CustomContainer>
    </>
  );
};

export default Genie;
