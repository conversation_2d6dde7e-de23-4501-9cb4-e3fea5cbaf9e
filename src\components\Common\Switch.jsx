"use client";

import React from "react";
import { twMerge } from "tailwind-merge";

export function Switch({
  checked = false,
  onCheckedChange,
  className,
  ...props
}) {
  return (
    <label className="tw-relative tw-inline-flex tw-items-center tw-cursor-pointer">
      <input
        type="checkbox"
        className="tw-sr-only"
        checked={checked}
        onChange={(e) => onCheckedChange?.(e.target.checked)}
        {...props}
      />
      <div
        className={twMerge(
          "tw-w-11 tw-h-6 tw-rounded-full tw-transition-colors",
          checked ? "tw-bg-primary-purple" : "tw-bg-gray-300",
          className
        )}
        // className={twMerge(
        //   "tw-w-11 tw-h-6 tw-bg-gray-300 tw-rounded-full tw-transition-colors peer-checked:!tw-bg-primary-purple",
        //   className
        // )}
      >
        <div
          className={`tw-absolute tw-top-0.5 tw-left-0.5 tw-bg-white tw-w-5 tw-h-5 tw-rounded-full tw-transition-transform ${
            checked ? "tw-translate-x-5" : ""
          }`}
        />
      </div>
    </label>
  );
}
