import { getOneCommunity } from "@/app/action";
import PostTab from "@/components/Communities/tabs/PostTab";
import { getServerOrigin } from "@/utils/ServerFunctions";

const PodRoom = async ({ params }) => {
  const { slug } = await params;
  const communityData = await getOneCommunity(slug);
  const origin = await getServerOrigin();
  //   console.log(slug);
  return (
    <div>
      <PostTab
        origin={origin}
        communityData={communityData?.data}
        communityPostVisiblity="podRoom"
      />
    </div>
  );
};

export default PodRoom;
