"use client";
import React from "react";
import Image from "next/image";
import { getYoutubeThumbnail } from "@/utils/function";
const PostDescription = ({ description, coverImage }) => {
  // Example usage:

  return (
    <div className="tw-w-full lg:tw-w-[87%]">
      {coverImage?.[0]?.isCustom && (
        <div className="tw-my-3 tw-relative tw-flex tw-justify-center">
          <Image
            src={
              coverImage?.[0]?.type === "video"
                ? getYoutubeThumbnail(coverImage?.[0]?.link)
                : coverImage?.[0]?.link
            }
            alt={"Cover Image"}
            className="tw-rounded-[1.5rem] !tw-relative tw-w-[28rem] lg:!tw-w-[50rem]"
            fill
          />
          {coverImage?.[0]?.type === "video" && (
            <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center ">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="tw-h-16 tw-w-16 tw-text-white"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </div>
          )}
        </div>
      )}
      <div id="" className="ql-editor tw-w-full tw-mb-40 lg:tw-mb-0 !tw-p-0">
        <div
          className=" !tw-relative project-description "
          dangerouslySetInnerHTML={{
            __html: description,
          }}
        />
      </div>
    </div>
  );
};

export default React.memo(PostDescription);
