import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import Stories, { WithSeeMore } from "react-insta-stories";
import { Eye, MoreVertical, Pencil, PenLine, Trash2 } from "lucide-react";
import {
  CloseModalIcon,
  HeartFillOutIcon,
  HeartIcon,
  StoryLikeIcon,
} from "@/utils/icons";
import VideoPlayer from "./VideoPlayer/VideoPlayer";
import HLSPlayer from "./VideoPlayer/HLSPlayer";
import dayjs from "dayjs";
import {
  createProxyImageUrl,
  fonts,
  formatNumber,
  generateAvatarCanvas,
  getTimePassedFromNow,
  parseColor,
} from "@/utils/function";
import { fontMap } from "./Font";
import NextImage from "next/image";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "nextjs-toploader/app";

import { Suspense, use, useCallback, useContext, useEffect } from "react";
import { useState } from "react";
import PopUpModal from "../Common/PopUpModal";
import useApiRequest from "../helper/hook/useApiRequest";
import {
  deleteHighlights,
  getStoryLikeAndViewCount,
  getLikedStoryUser,
  likeStory,
  updateHighlights,
} from "@/app/action";
import { safeToast } from "@/utils/safeToast";
import AddHighlightModal from "../Profile/AddHighlightModal";
import AnimatedAlignPositioned from "./AnimatedAlignPositioned";
import { valContext } from "../context/ValContext";
import authStorage from "@/utils/API/AuthStorage";
import UserAvatar from "../Common/UserAvatar";
import SelectProjectSkeleton from "../Loader/SelectProjectSkeleton";
import InfiniteScroll from "react-infinite-scroll-component";
import CustomSeeMore from "./CustomSeeMore";

const RenderProperties = ({ properties, action, router }) => {
  /* Render all properties */
  return properties?.map((prop, index) => {
    const dx = prop?.position?.dx ?? 0;
    const dy = prop?.position?.dy ?? 0;
    const backGroundColor = prop?.backGroundColor ?? "transparent";
    const text = prop?.text ?? "";
    const fontSize = prop?.fontSize ?? 16;
    const textColor = prop?.textColor ?? "#000";
    const scale = prop?.scale ?? 1;
    const rotation = prop?.rotation ?? 0;
    const fontFamily = prop?.fontFamily ?? "default";
    const slug = prop?.slug;
    const textAlignPosition = {
      0: "center",
      1: "left",
      2: "right",
    };

    const selectedFont = fonts[fontFamily];
    const fontClass = fontMap[selectedFont]?.className ?? "";

    return (
      <div className="tw-w-full tw-h-full tw-absolute " key={index}>
        <AnimatedAlignPositioned
          dx={dx}
          dy={dy}
          alignment={textAlignPosition[0]}
          rotateDegrees={rotation}
          duration={0}
          curve="easeInOut"
        >
          <Popover
            key={index}
            onOpenChange={(open) => {
              if (open) {
                action("pause");
              } else {
                action("play");
              }
            }}
          >
            <PopoverTrigger asChild>
              <div
                className={fontClass}
                style={{
                  // position: "absolute",
                  // top: `calc(55% + ${dy * 50}%)`, // convert -1..1 to percent offset from center
                  // left: `calc(55% + ${dx * 50}%)`,
                  // transform: `translate(-50%, -50%) scale(${scale}) rotate(${rotation}rad)`,
                  transform: `scale(${scale}) rotate(${rotation}rad)`,
                  color: parseColor(textColor),
                  fontSize: `${fontSize / 1.25}px`,
                  fontWeight: "500",
                  padding: "0.6rem",
                  borderRadius: "10px",
                  backgroundColor: parseColor(backGroundColor),
                  cursor: "pointer",
                  pointerEvents: "auto",
                  zIndex: 999999 + index, // Ensure each element has unique z-index
                  // maxWidth: "80%",
                  wordWrap: "break-word",
                  // width: "100%",
                  textAlign: textAlignPosition[prop?.textAlign ?? 0],
                }}
              >
                {text}
              </div>
            </PopoverTrigger>
            <PopoverContent
              side="top"
              align="center"
              className="tw-max-w-xs tw-p-4 tw-text-center "
            >
              <p
                onClick={() => {
                  if (slug) {
                    router.push(`/projects/${slug}`);
                  }
                }}
              >
                {text}
              </p>
            </PopoverContent>
          </Popover>
        </AnimatedAlignPositioned>
      </div>
    );
  });
};
// Custom Header Component with 3-dot settings
const CustomStoryHeader = ({
  profileImage,
  heading,
  subheading,
  currentUser,
  onAction,
  ele,
  action,
  setCurrentAction,
  isSettingVisible = false,
  router,
}) => {
  const settings = [
    {
      label: "Edit Highlight",
      icon: <Pencil stroke="#2D394A" size={17} />,
      onClick: () => {
        action("pause");
        setCurrentAction(action);
        onAction("edit", currentUser);
      },
    },
    {
      label: "Remove Story",
      className: "tw-text-[#EF3B41] hover:tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        onAction("delete", ele);
      },
    },
  ];

  return (
    <div className="tw-inline-block">
      <div
        onClick={() => {
          if (currentUser?.slug) {
            action("pause");
            router?.push(`/user/${currentUser?.slug}`);
          }
        }}
        className={`tw-absolute ${
          currentUser?.slug && "tw-cursor-pointer"
        } tw-top-2 tw-left-0  tw-flex tw-items-center tw-gap-2.5 tw-p-4 tw-z-[9999] tw-w-full`}
      >
        <div className="tw-relative tw-w-12 tw-h-12 tw-rounded-full">
          <NextImage
            src={profileImage}
            alt="User"
            className="!tw-rounded-full !tw-object-cover tw-border-2 tw-border-white"
            fill
          />
        </div>
        <div className="tw-flex-1">
          <p className="tw-text-[#ffffffe6] tw-font-medium">{heading}</p>
          <p className="tw-text-[#fffc] tw-text-[.6rem]">{subheading}</p>
        </div>

        {isSettingVisible && (
          <DropdownMenu
            onOpenChange={(open) => {
              if (open) {
                // Pause story when dropdown opens
                setCurrentAction(action);
                action("pause");
                // console.log("Dropdown opened - story paused");
              } else {
                // Resume story when dropdown closes (optional)
                // console.log("Dropdown clo/sed");
              }
            }}
          >
            <DropdownMenuTrigger asChild>
              <div
                onClick={(e) => {
                  e.stopPropagation();
                  // console.log("Dropdown trigger clicked");
                }}
                className="tw-text-white tw-p-2 hover:tw-bg-white/20 tw-rounded-full tw-transition-colors tw-cursor-pointer"
              >
                <MoreVertical size={20} />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="tw-z-[99999] tw-bg-white tw-border tw-border-gray-200 tw-rounded-md tw-shadow-lg tw-min-w-[160px]"
              align="end"
              side="bottom"
              sideOffset={8}
            >
              {settings.map((setting, index) => (
                <DropdownMenuItem
                  key={index}
                  className={`tw-flex tw-items-center tw-gap-3 tw-px-3 tw-py-2.5 tw-cursor-pointer tw-text-sm hover:tw-bg-gray-50 focus:tw-bg-gray-50 tw-transition-colors ${
                    setting.className || ""
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setting.onClick(ele);
                  }}
                >
                  <span className="tw-flex tw-items-center tw-justify-center tw-w-4 tw-h-4">
                    {setting.icon}
                  </span>
                  <span className="tw-flex-1">{setting.label}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
};

const ModifyStoryPreview = ({
  isOpen,
  setIsOpen,
  currentUserIndex,
  setCurrentUserIndex = () => {},
  storyData,
  dataKey = "Stories",
  setReload = () => {},
  setStoryData = () => {},
  isSettingVisible = false,
  isHighlight = false,
  loginUserData,
}) => {
  const router = useRouter();
  const [deleteData, setDeleteData] = useState(null);
  const [isPaused, setIsPaused] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);
  const [stories, setStories] = useState([]);
  const [isHighlighModalOpen, setIsHighlighModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [userList, setUserList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });

  const resetDataList = () => {
    setUserList([]);
    setPagination({
      page: 1,
      limit: 10,
      total: 0,
    });
  };
  const [isUserListOpen, setIsUserListOpen] = useState({
    isOpen: false,
    storyId: null,
  });
  const [visitedStory, setVisitedStory] = useState({});

  // Function to return visited story
  const findAndStoreStory = (storyId) => {
    storyData?.forEach((record) => {
      const matched = record?.Stories?.find((story) => story?.id === storyId);

      if (matched) {
        setVisitedStory((prev) => {
          const existing = prev[record.id] || [];
          // avoid duplicates
          if (existing.includes(matched.id)) return prev;

          return {
            ...prev,
            [record.id]: [...existing, matched.id],
          };
        });
      }
    });
  };

  // const { loginUserData } = useContext(valContext);
  const api = useApiRequest(false);
  // API instance to fetch user list
  const api2 = useApiRequest();

  const getStoryLikeUserList = (story, action, page = null) => {
    setIsUserListOpen({
      isOpen: true,
      storyId: story?.id,
      action,
    });
  };

  const fetchUserList = (storyId) => {
    const queryParams = {
      storyId,
      page: pagination.page,
      limit: pagination.limit,
      sortBy: "isLike",
    };
    api2.sendRequest(
      getLikedStoryUser,
      (res) => {
        const user =
          userList?.length > 0 && pagination.page > 1
            ? [...userList, ...res?.data?.rows]
            : [...res?.data?.rows];
        setUserList(user);
        setPagination((prev) => ({
          ...prev,
          total: res?.data?.count,
        }));
      },
      queryParams
    );
  };

  // Handler for settings actions
  const handleHeaderAction = (type, data) => {
    setIsPaused(true);
    switch (type) {
      case "delete":
        // console.log("Muting user:", data);
        setDeleteData({
          ...data,
        });
        break;
      case "edit":
        setIsHighlighModalOpen(true);
        setEditData(data);
        break;

      default:
      // console.log("Unknown action:", type);
    }
  };

  const navigate = (direction) => {
    if (direction === "next") {
      // Go to next user
      if (currentUserIndex < storyData.length - 1) {
        setCurrentUserIndex((prev) => prev + 1);
      }
    } else if (direction === "prev") {
      // Go to previous user
      if (currentUserIndex > 0) {
        setCurrentUserIndex((prev) => prev - 1);
      }
    }
  };

  const updateStoryLikeAndView = (payload = {}) => {
    api.sendRequest(
      likeStory,
      (res) => {
        // console.log(res);
      },
      payload,
      "",
      (err) => {
        // console.log("Error", err);
      }
    );
  };

  // Update Story Like

  // Render Story Content
  const renderStoryContent = (story, header, currentUser) => {
    const { overlayImage, properties = [] } = story?.Story ?? story;

    return ({ action, story }) => (
      <WithSeeMore story={story} action={action}>
        <div className="tw-w-full tw-h-full">
          {header && (
            <CustomStoryHeader
              profileImage={header?.profileImage}
              heading={header?.heading}
              subheading={header?.subheading}
              currentUser={currentUser}
              onAction={handleHeaderAction}
              ele={{
                id: currentUser?.id,
                storyId: story?.Story?.id,
              }}
              action={action}
              setCurrentAction={setCurrentAction}
              isSettingVisible={isSettingVisible}
              router={router}
            />
          )}
          <div
            style={{
              width: "100%",
              height: "100%",
              position: "relative",
            }}
          >
            {/* Use Next.js Image for faster loading */}
            {overlayImage ? (
              <NextImage
                src={overlayImage}
                alt="Story background"
                fill
                className="tw-object-contain"
                priority
                sizes="100vw"
                quality={85}
                onError={(e) => {
                  // console.error("Failed to load overlay image:", overlayImage);
                  // Fallback to background image if NextImage fails
                  e.target.style.display = "none";
                  e.target.parentElement.style.backgroundImage = `url(${overlayImage})`;
                  e.target.parentElement.style.backgroundSize = "cover";
                  e.target.parentElement.style.backgroundPosition = "center";
                }}
              />
            ) : (
              <div
                style={{
                  width: "100%",
                  height: "100%",
                  backgroundColor: "#f0f0f0",
                }}
              />
            )}
            <RenderProperties
              action={action}
              router={router}
              properties={properties}
            />
          </div>
        </div>
      </WithSeeMore>
    );
  };

  const currentUser = storyData[currentUserIndex];

  // Function to create stories from current user data
  const createStoriesFromData = (currentUser, dataKey, counts) => {
    return currentUser?.[dataKey]
      ?.map((story, i) => {
        const { mediaLink, mediaType, overlayImage } = story?.Story ?? story;
        // Safety check for required story data
        if (!story || (!mediaLink && !overlayImage)) {
          return null;
        }
        const userName = `${currentUser?.title ?? currentUser?.firstName} ${
          currentUser?.lastName ?? ""
        }`;
        const header = {
          heading: userName,
          profileImage: currentUser.image
            ? createProxyImageUrl(currentUser.image)
            : generateAvatarCanvas(userName, 100),
          subheading: getTimePassedFromNow(
            isHighlight
              ? currentUser?.createdAt
              : story?.Story?.createdAt ?? story?.createdAt
          ),
        };

        const storyId = story?.Story?.id ?? story?.id;

        const defaultStory = {};

        if (dataKey === "Stories") {
          defaultStory.seeMoreCollapsed = ({ toggleMore, action }) => (
            <>
              <CustomSeeMore
                counts={counts[storyId] ?? {}}
                story={story}
                action={action}
                isCreatedByCurrentUser={+loginUserData?.id === +story?.UserId}
                viewHandler={() => {
                  getStoryLikeUserList(story, action);
                }}
                likeHandler={(isLike) => {
                  updateStoryLikeAndView({
                    storyId: story?.id,
                    isLike,
                  });
                }}
              />
            </>
          );
          defaultStory.seeMore = ({ close }) => <></>;
          defaultStory.storyCount = counts[storyId] ?? {};
          defaultStory.isViewByCurrentUser = story?.isViewByCurrentUser;
          defaultStory.userId = story?.UserId;
        }

        if (mediaType === "video") {
          if (mediaLink.endsWith(".m3u8")) {
            const hlsStory = {
              content: ({ action, story }) => (
                <WithSeeMore story={story} action={action}>
                  <HLSPlayer
                    overlayImage={overlayImage}
                    url={mediaLink}
                    action={action}
                    header={header}
                    customHeader={
                      <CustomStoryHeader
                        profileImage={header?.profileImage}
                        heading={header?.heading}
                        subheading={header?.subheading}
                        currentUser={currentUser}
                        onAction={handleHeaderAction}
                        ele={{
                          id: currentUser?.id,
                          storyId,
                        }}
                        router={router}
                        action={action}
                        setCurrentAction={setCurrentAction}
                        isSettingVisible={isSettingVisible}
                      />
                    }
                    properties={
                      <RenderProperties
                        action={action}
                        router={router}
                        properties={
                          story?.Story?.properties ?? story?.properties
                        }
                      />
                    }
                  />
                </WithSeeMore>
              ),
              duration: story?.duration ? story?.duration * 1000 : 8000,
              header,
              storyId,
            };
            return { ...hlsStory, ...defaultStory };
          }
          const videoStory = {
            content: ({ action, story }) => (
              <WithSeeMore story={story} action={action}>
                <VideoPlayer
                  overlayImage={overlayImage}
                  url={mediaLink}
                  action={action}
                  header={header}
                  customHeader={
                    <CustomStoryHeader
                      profileImage={header?.profileImage}
                      heading={header?.heading}
                      subheading={header?.subheading}
                      currentUser={currentUser}
                      onAction={handleHeaderAction}
                      ele={{
                        id: currentUser?.id,
                        storyId,
                      }}
                      action={action}
                      router={router}
                      setCurrentAction={setCurrentAction}
                      isSettingVisible={isSettingVisible}
                    />
                  }
                  properties={
                    <RenderProperties
                      action={action}
                      router={router}
                      properties={story?.Story?.properties ?? story?.properties}
                    />
                  }
                />
              </WithSeeMore>
            ),
            duration: story?.duration ? +story?.duration * 1000 : 8000,
            header,
            storyId,
          };
          // For mp4/webm
          return { ...videoStory, ...defaultStory };
        }

        // likeCount isLikedByCurrentUser

        const imageStory = {
          content: renderStoryContent(story, header, currentUser),
          duration: story?.duration ? +story?.duration * 1000 : 8000,
          storyId,
        };

        return { ...imageStory, ...defaultStory };
      })
      .filter(Boolean); // Remove any null/undefined stories
  };

  useEffect(() => {
    if (isUserListOpen?.storyId) {
      // setIsPaused(true);
      fetchUserList(isUserListOpen?.storyId);
    }
  }, [isUserListOpen.storyId, pagination.page]);

  // Update stories when currentUser or dataKey changes
  // useEffect(() => {
  //   const newStories = createStoriesFromData(currentUser, dataKey);
  //   setStories(newStories);
  // }, [currentUser, dataKey]);
  useEffect(() => {
    if (!currentUser) return;

    async function buildStoriesWithCounts() {
      const counts = {};
      if (dataKey === "Stories") {
        for (const story of currentUser?.[dataKey] ?? []) {
          if (+loginUserData?.id === +story?.UserId) {
            try {
              const res = await getStoryLikeAndViewCount({
                storyId: story.id,
              });
              counts[story.id] = res?.data;
            } catch (err) {
              console.error(err);
            }
          }
        }
      }

      // Only build stories after counts are fetched
      setStories(createStoriesFromData(currentUser, "Stories", counts));
    }

    buildStoriesWithCounts();
  }, [currentUser, loginUserData?.id, dataKey]);

  // Preload images for faster rendering
  useEffect(() => {
    if (!storyData || storyData.length === 0) return;

    const loadAllImage = (imageArray) => {
      imageArray?.forEach((story) => {
        if (story?.Story?.overlayImage ?? story?.overlayImage) {
          const img = new Image();
          img.src = story?.Story?.overlayImage ?? story?.overlayImage;
        }
        if (
          (story?.Story?.mediaLink && story?.Story?.mediaType === "image") ||
          (story?.mediaLink && story?.mediaType === "image")
        ) {
          const img = new Image();
          img.src = story?.Story?.mediaLink ?? story.mediaLink;
        }
      });
    };

    // console.log("Preload images");
    const preloadImages = () => {
      // Preload current user's images
      const currentUser = storyData[currentUserIndex];

      loadAllImage(currentUser[dataKey]);
      // Preload next user's first image
      if (currentUserIndex < storyData.length - 1) {
        const nextUser = storyData[currentUserIndex + 1];
        const firstStory = nextUser[dataKey]?.[0];
        // console.log(firstStory, "Next User Story", nextUser);
        loadAllImage(nextUser[dataKey]);
      }

      // Preload previous user's last image
      if (currentUserIndex > 0) {
        const prevUser = storyData[currentUserIndex - 1];
        const lastStory = prevUser[dataKey]?.[prevUser[dataKey].length - 1];
        loadAllImage(prevUser[dataKey]);
      }
    };

    preloadImages();
  }, [currentUserIndex, storyData, isOpen]);

  // useEffect(() => {
  //   if (!isOpen) {
  //     setTimeout(() => {
  //       setStories([]);
  //     }, 300);
  //   }
  // }, [isOpen]);

  return (
    <div>
      {/* Highlight modal */}
      <AddHighlightModal
        isOpen={isHighlighModalOpen}
        setIsOpen={setIsHighlighModalOpen}
        setRefresh={setReload}
        editData={editData}
      />
      {/* User List Modal */}
      <Modal
        open={isUserListOpen.isOpen}
        onClose={() => {
          setIsUserListOpen((prev) => {
            prev?.action("play");
            return {
              // ...prev,
              isOpen: false,
              storyId: null,
            };
          });
          resetDataList();
        }}
        center
        classNames={{
          modal:
            "!tw-max-w-full !tw-w-[30%] !tw-rounded-[1.25rem] !tw-rounded-[1.25rem]",
          closeButton: "!tw-z-[999999]",
        }}
      >
        <p className="tw-text-2xl tw-font-bold tw-text-primary-black">
          Viewers
        </p>
        {/* this is the User list Modal of story id {isUserListOpen?.storyId} */}
        <div className="tw-h-[25rem] tw-overflow-auto tw-mt-5">
          {api2.isLoading ? (
            <>
              <SelectProjectSkeleton count={7} />
            </>
          ) : (
            <div id="scrollableDiv" className="tw-h-[26rem]">
              <InfiniteScroll
                dataLength={userList?.length ?? 0}
                scrollableTarget="scrollableDiv"
                className="infinite-scrollbar tw-px-1"
                hasMore={
                  pagination?.page <
                  Math.ceil(pagination?.total / pagination?.limit)
                  // true
                }
                next={() => {
                  setPagination((prev) => ({
                    ...prev,
                    page: prev?.page + 1,
                  }));
                }}
                loader={<SelectProjectSkeleton count={3} />}
              >
                {userList?.map((ele) => (
                  <div
                    key={ele?.id}
                    className="tw-flex tw-gap-3 tw-items-center tw-font-medium tw-text-xl tw-mb-3"
                  >
                    <div className="tw-relative">
                      <UserAvatar
                        imageUrl={ele?.User?.image}
                        userName={ele?.User?.firstName}
                      />
                      {ele?.isLike && (
                        <div className="tw-absolute tw-bottom-0 tw-right-0">
                          <StoryLikeIcon />
                        </div>
                      )}
                    </div>
                    {/* Name */}
                    <p className="">
                      {ele?.User?.firstName + ele?.User?.lastName}
                    </p>
                  </div>
                ))}
              </InfiniteScroll>
            </div>
          )}
        </div>
      </Modal>
      {/* Stories Preview Modal */}
      <Modal
        open={isOpen}
        onClose={() => {
          setIsOpen(false);

          if (typeof setCurrentUserIndex === "function") {
            setCurrentUserIndex(0);
          }

          // Update visited stories
          if (dataKey === "Stories") {
            setStoryData((prev) =>
              prev?.map((record) => {
                // does this recordId exist in matches?
                if (!visitedStory?.[record?.id]) return record;

                // copy stories and update matching ones
                const updatedStories = record?.Stories?.map((story) => {
                  if (visitedStory?.[record?.id]?.includes(story?.id)) {
                    return { ...story, isViewByCurrentUser: true };
                  }
                  return story;
                });

                return { ...record, Stories: updatedStories };
              })
            );
          }
        }}
        center
        classNames={{
          modal:
            "!tw-p-0 !tw-m-0 !tw-bg-black !tw-text-white !tw-w-full !tw-h-screen",
          closeButton: "!tw-z-[99999]",
        }}
        //   closeIcon={<X className="tw-text-white" />}
        closeIcon={
          <div className="tw-relative tw-top-[5rem] tw-right-[1.5rem] md:tw-static ">
            <CloseModalIcon size={30} />
          </div>
        }
        styles={{ modal: { maxWidth: "100vw", width: "100%", padding: 0 } }}
        showCloseIcon={true}
        focusTrapped={false}
      >
        <PopUpModal
          isLoading={api.isLoading}
          contentClassName="!tw-z-[99999]"
          isOpen={deleteData}
          confirmButtonText={stories?.length > 1 ? "Remove" : "Delete"}
          setIsOpen={(value) => {
            setDeleteData(value);
            // Resume story when modal is closed without confirming
            if (!value && currentAction && isPaused) {
              currentAction("play");
              setIsPaused(false);
            }
          }}
          mainMessage={
            stories?.length > 1
              ? "Remove this story?"
              : "Delete This Highlight?"
          }
          subMessage={`Are you sure you want to ${
            stories?.length > 1 ? "remove this story" : "delete this highlight"
          }? Once removed , it cannot be recovered.`}
          onConfirm={() => {
            const apiCall =
              stories?.length > 1 ? updateHighlights : deleteHighlights;
            let payload =
              stories?.length > 1
                ? {
                    id: deleteData?.id,
                    RemoveStoriesIds: [deleteData?.storyId],
                  }
                : {
                    id: deleteData?.id,
                  };
            // console.log(payload);
            api.sendRequest(
              apiCall,
              (res) => {
                // console.log(res);

                if (stories?.length > 1) {
                  safeToast.success("Highlight updated successfully");
                  // Remove the deleted story from the stories state
                  setStories((prevStories) =>
                    prevStories.filter(
                      (story) => story.storyId !== deleteData?.storyId
                    )
                  );
                } else {
                  safeToast.success(res?.message);
                  setReload((prev) => !prev);
                  setIsOpen(false);
                }

                // resetState();
                setDeleteData(null);
                setIsPaused(false);
                // Don't resume story after deletion - let it continue naturally
              },
              payload,
              ""
            );
          }}
        />
        <div className="tw-relative tw-w-full tw-h-screen tw-flex tw-items-center tw-justify-center">
          <Stories
            key={`user-${currentUserIndex}`}
            stories={stories}
            defaultInterval={6000}
            storyContainerStyles={{
              overflow: "hidden",
            }}
            keyboardNavigation
            height="95vh"
            onStoryStart={(_, value) => {
              // console.log(value, "Story is Open");
              if (dataKey === "Stories") {
                findAndStoreStory(value?.storyId);
                // setStoryData((prev) =>
                //   prev?.map((ele) =>
                //     ele?.id !== value?.userId
                //       ? ele
                //       : {
                //           ...ele,
                //           Stories: ele?.Stories?.map((story) =>
                //             story?.id !== value?.storyId
                //               ? story
                //               : {
                //                   ...story,
                //                   isViewByCurrentUser: true,
                //                 }
                //           ),
                //         }
                //   )
                // );

                // data;
              }
              updateStoryLikeAndView({
                storyId: value?.storyId,
              });
            }}
            onAllStoriesEnd={() => {
              if (currentUserIndex < storyData.length - 1) {
                setCurrentUserIndex((prev) => prev + 1);
              } else {
                setIsOpen(false);
              }
            }}
          />

          {/* Navigation buttons */}
          {/* {currentUserIndex > 0 && (
            <button
              onClick={() => navigate("prev")}
              className="tw-z-[9999] tw-absolute tw-left-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-white tw-text-center hover:tw-bg-[#5d5a58] tw-transition-colors"
            >
              <ChevronLeft size={25} />
            </button>
          )}
          {currentUserIndex < storyData.length - 1 && (
            <button
              onClick={() => navigate("next")}
              className="tw-z-[9999] tw-absolute tw-right-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-center tw-text-white hover:tw-bg-[#5d5a58] tw-transition-colors"
            >
              <ChevronRight size={25} />
            </button>
          )} */}
        </div>
      </Modal>
    </div>
  );
};

export default ModifyStoryPreview;
