import { CloseModalIcon, ProjectImageUploadIcon } from "@/utils/icons";
import { useFormik } from "formik";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useEffect, useState } from "react";
import Modal from "react-responsive-modal";
import CustomButton from "@/components/Common/Custom-Button";
import GooglePlacesAutocomplete from "react-google-places-autocomplete";
import Image from "next/image";
import toast from "react-hot-toast";
import { API_BASE_URL, apiGenerator, isValidURL } from "@/utils/function";
import { addCommunity, editCommunity, updateImageToURL } from "@/app/action";
import * as Yup from "yup";
import FloatingLabelInput from "../HomePage/Form/FloatingLabelInput";
import FloatingLabelTextArea from "../HomePage/Form/FloatingLabelTextArea";
import useApiRequest from "../helper/hook/useApiRequest";
import "react-responsive-modal/styles.css";
import { safeToast } from "@/utils/safeToast";

const CommunitiesModal = ({
  open,
  setOpen,
  setRefresh = () => {},
  reFetchData = () => {},
  editData = null,
  setEditData = () => {},
  modalTitle,
  modalSubmitButton,
}) => {
  const [isAddMore, setIsAddMore] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [isFocused, setIsFocused] = useState(false);
  const [location, setLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const api = useApiRequest();

  // Reset Modal
  const resetModal = () => {
    if (editData) {
      setEditData(null);
    } else {
      setOpen(false);
    }
    formik.resetForm();
    setIsAddMore(false);
    setSelectedImage(null);
    setPreviewImage(null);
    setIsFocused(null);
    setLocation(null);
    setIsLoading(false);
  };
  // validationSchema;
  const validationSchema = Yup.object().shape({
    // name: Yup.string().trim().required("Community Name is required"),
    name: Yup.string()
      .trim()
      .required("Community Name is required")
      .max(50, "Maximum 50 characters allowed")
      .matches(
        /^(?=.*[A-Za-z])[A-Za-z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?\s]*$/,
        "Must include at least one letter"
      ),
  });

  // console.log(subProjectList);
  // Form Submit Handler
  const formSubmitHandler = async (values) => {
    setIsLoading(true);
    // console.log("Updated Profile:", values);
    let payload = {};
    Object.keys(values)?.forEach((ele) => {
      payload = {
        ...payload,
        [ele]: values[ele]?.trim() === "" ? null : values[ele]?.trim(),
      };
    });
    // if (location) {
    payload = {
      ...payload,
      // location: location?.label,
      location: location && location?.label !== "" ? location?.label : null,
    };
    // }
    if (selectedImage) {
      if (isValidURL(selectedImage)) {
        payload = {
          ...payload,
          image: selectedImage,
        };
      } else {
        // Upload from device
        const formData = new FormData();
        formData.append("file", selectedImage);

        try {
          const res = await updateImageToURL(formData);
          setSelectedImage(res?.data?.[0]?.link);
          payload = {
            ...payload,
            image: res?.data?.[0]?.link,
          };
        } catch (error) {
          console.dir(error);
          return;
        }
        // console.dir(payload);
      }
    } else if (editData && !selectedImage) {
      payload = {
        ...payload,
        image: null,
      };
    }

    // console.log(payload);

    if (editData) {
      api.sendRequest(
        editCommunity,
        (res) => {
          resetModal();
          setRefresh((prev) => !prev);
          reFetchData();
        },
        {
          ...payload,
          id: editData?.id,
        },

        "Community updated successfully",
        () => {
          // Error Handler
          setIsLoading(false);
        }
      );
    } else {
      // Create the New Project
      api.sendRequest(
        addCommunity,
        (res) => {
          resetModal();
          setRefresh((prev) => !prev);
          reFetchData();
        },
        payload,

        "Community created successfully",
        () => {
          // Error Handler
          setIsLoading(false);
        }
      );
    }
  };
  // Formik
  const formik = useFormik({
    enableReinitialize: true, // Allows form values to update when `open` changes
    initialValues: {
      name: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      // console.log("Updated Profile:", values);

      formSubmitHandler(values);
    },
  });

  // Preview Image
  const handleProfileImage = (e) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }
    // const fileSize = e.target.files[0].size / (1024 * 1024);
    // if (fileSize > 2) {
    //   safeToast.error("The file size exceeds 2 MB. Please upload a smaller file.");
    //   return;
    // }
    setSelectedImage(e.target.files[0]);

    if (e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        // dispatch({ type: "userProfileEdit", payload: reader.result });
        setPreviewImage(reader.result);
      };
      reader?.readAsDataURL(e.target.files[0]);
    }
  };

  useEffect(() => {
    if (editData) {
      formik.setFieldValue("name", editData?.name);
      if (editData?.image) {
        setPreviewImage(editData?.image);
        setSelectedImage(editData?.image);
      }
      if (editData?.location) {
        setLocation({
          label: editData?.location,
          value: editData?.location,
        });
      }
      if (editData?.description) {
        formik.setFieldValue("description", editData?.description);
      }
    }
  }, [editData, editData?.id]);
  return (
    <>
      <Modal
        classNames={{
          modal:
            "!tw-max-w-[95vw] !tw-w-full sm:!tw-max-w-[90vw] md:!tw-max-w-[80vw] lg:!tw-max-w-[40rem] !tw-m-2 md:tw-m-[1.2rem]  !tw-rounded-[1.25rem] !tw-mt-1",
          closeButton: `${isLoading && "!tw-cursor-not-allowed"}`,
          overlay: "!tw-bg-[#000000CC]",
        }}
        center
        focusTrapped={false}
        // open={editData ? editData !== null : open}
        open={!!editData || open}
        onClose={() => {
          if (!isLoading) {
            resetModal();
          }
        }}
      >
        <div className="tw-py-5 tw-mx-4 lg:tw-mx-14">
          <h2 className="tw-text-xl md:tw-text-2xl tw-text-center tw-font-semibold">
            {/* {editData ? "Edit" : "Create"} Community */}
            {modalTitle}
          </h2>
          <p className="tw-text-center tw-text-primary-black tw-max-w-[22rem] tw-mx-auto tw-font-light tw-text-sm md:tw-text-base">
            Create a new community to discuss with others who share the same
            interests as you.
          </p>
          <div className="tw-h-[60vh] sm:tw-h-[65vh] md:tw-h-[70vh] lg:tw-h-[35rem] tw-overflow-auto tw-pr-2">
            <form onSubmit={formik.handleSubmit} className="tw-mt-4">
              {/* Community Name */}
              <FloatingLabelInput
                label="Community Name*"
                name="name"
                formik={formik}
                maxLength={51}
              />
              {formik.touched.name && formik.errors.name && (
                <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                  {formik.errors.name}
                </p>
              )}
              <div
                onClick={() => {
                  setIsAddMore((prev) => !prev);
                }}
                className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center"
              >
                <p className="tw-text-primary-black tw-text-lg">
                  Add More (Optional)
                </p>
                {!isAddMore ? <ChevronDown /> : <ChevronUp />}
              </div>

              <div
                className={`tw-overflow-hidden tw-transition-all tw-duration-300 ${
                  isAddMore
                    ? "tw-max-h-[900px] tw-opacity-100"
                    : "tw-max-h-0 tw-opacity-0"
                }`}
              >
                {/* Description */}
                <FloatingLabelTextArea
                  label="Description"
                  name="description"
                  formik={formik}
                />
                <div className="tw-flex tw-justify-end tw-mb-4">
                  <p className="tw-text-sm tw-text-[#787E89] ">
                    {formik.values.description?.length ?? 0}/250
                  </p>
                </div>

                {/* Location */}
                <div className="tw-mb-4">
                  <div className="tw-relative  tw-bg-[#F1F2F3] tw-pt-5 tw-pb-3 tw-px-5 tw-rounded-2xl">
                    {/* Floating Label */}
                    <label
                      htmlFor="location"
                      className={`tw-absolute tw-left-5 tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none
          ${
            isFocused || location
              ? "tw-top-2 tw-text-xs tw-text-gray-500"
              : "tw-top-6 tw-text-lg"
          }`}
                    >
                      Location
                    </label>

                    <GooglePlacesAutocomplete
                      apiKey={process.env.NEXT_PUBLIC_GOOGLE_API_KEY}
                      selectProps={{
                        value: location,
                        onChange: (value) => setLocation(value),
                        onFocus: () => setIsFocused(true),
                        onBlur: () => setIsFocused(!!location),
                        placeholder: " ", // keep it blank for floating label trick
                        isClearable: true,
                        name: "location",
                        styles: {
                          control: (base, state) => ({
                            ...base,
                            backgroundColor: "transparent",
                            border: "none",
                            boxShadow: "none",
                            padding: "0",
                            minHeight: "2.5rem",
                            color: "#2D394A",
                            fontSize: "1.125rem", // Tailwind's text-lg
                            fontWeight: 600,
                          }),
                          singleValue: (base) => ({
                            ...base,
                            color: "#2D394A",
                          }),
                          placeholder: (base) => ({
                            ...base,
                            // color: "#BFBFC7",
                          }),
                          indicatorSeparator: () => ({}),
                          dropdownIndicator: () => ({
                            display: "none",
                          }),
                          menu: (base) => ({
                            ...base,
                            zIndex: 100,
                          }),
                        },
                      }}
                    />
                  </div>
                  <p className="tw-italic tw-text-[#787E89] tw-font-light tw-text-sm tw-text-left">
                    Add a location to find specific Community easily.
                  </p>
                </div>

                {/* Upload Image */}
                {previewImage ? (
                  <div className="tw-relative tw-overflow-visible tw-p-1.5">
                    <button
                      onClick={() => {
                        setPreviewImage(null);
                        setSelectedImage(null);
                      }}
                      className="tw-absolute tw-cursor-pointer tw-z-10 -tw-top-1 -tw-right-0"
                    >
                      <CloseModalIcon stroke="black" fill="#d7d4b2" size={25} />
                    </button>
                    <div className="tw-w-full tw-max-w-full tw-overflow-hidden tw-rounded-2xl">
                      <Image
                        src={previewImage}
                        alt="community-image"
                        className="!tw-rounded-2xl tw-w-full tw-h-auto tw-max-h-[200px] sm:tw-max-h-[250px] md:tw-max-h-[300px] tw-object-cover"
                        width={440}
                        height={271}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="tw-relative tw-overflow-hidden tw-mb-4">
                    <div className="tw-cursor-pointer tw-relative tw-overflow-hidden tw-flex tw-justify-center">
                      <div className="tw-scale-75 sm:tw-scale-90 md:tw-scale-100">
                        <ProjectImageUploadIcon />
                      </div>
                      <input
                        type="file"
                        // accept="image/*"
                        accept=".jpg, .jpeg, .png"
                        className="tw-absolute !tw-cursor-pointer tw-inset-0 tw-w-full tw-h-full tw-opacity-0 tw-z-10"
                        onChange={handleProfileImage}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="tw-flex tw-justify-center tw-mb-2 tw-mt-5 ">
                <CustomButton
                  loading={isLoading}
                  className={"!tw-px-9 !tw-py-[14px] "}
                  type="submit"
                  count={8}
                >
                  <span className={"!tw-text-base"}>
                    {/* {editData ? "Update" : "Create"} */}
                    {modalSubmitButton}
                  </span>
                </CustomButton>
              </div>
            </form>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default CommunitiesModal;
