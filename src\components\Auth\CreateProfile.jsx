"use client";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import logo from "../../../public/images/logo/logo-primary.svg";
import CustomTitle from "../Common/CustomTitle";
import GlobalForm from "../Common/Custom-Form";
import Link from "next/link";
import * as Yup from "yup";
import { CustomContainer } from "../Common/Custom-Display";
import useApiRequest from "../helper/hook/useApiRequest";
import { completeUserProfile, updateImageToURL } from "@/app/action";
// import { useRouter } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import toast from "react-hot-toast";
import authStorage from "@/utils/API/AuthStorage";
import { eraseCookie, getCookie } from "@/utils/Cookies";
import { safeToast } from "@/utils/safeToast";

import { useFormik } from "formik";
import CustomButton from "../Common/Custom-Button";
import FloatingLabelTextArea from "../HomePage/Form/FloatingLabelTextArea";
import FloatingLabelInput from "../HomePage/Form/FloatingLabelInput";
import { EditPencileIcon, UserProfileIcon } from "@/utils/icons";
import GooglePlacesAutocomplete from "react-google-places-autocomplete";
import { ChevronDown, ChevronUp } from "lucide-react";
import { isBase64Image } from "@/utils/function";
import WebAppLogo from "../Common/WebAppLogo";

const CreateProfile = () => {
  const api = useApiRequest();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [isFocused, setIsFocused] = useState(false);
  const [location, setLocation] = useState(null);
  const [isAddMore, setIsAddMore] = useState(false);
  const [isLinkedinState, setIsLinkedinState] = useState(false);
  const [userData, setUserData] = useState(null);
  // const [isNewUser, SetIsNewUser] = useLocalStorage("isNewUser");

  const initialValues = {
    firstName: "",
    lastName: "",
    about: "",
    email: "",
  };

  const handleFormSubmit = async (values, actions) => {
    setIsLoading(true);
    let payload = {
      ...values,
      location: location && location?.label !== "" ? location?.label : null,
    };

    //   Object.keys(values).forEach((ele) => {
    //     if (ele !== "image" && values[ele]) {
    //       payload = {
    //         ...payload,
    //         [ele]: values[ele]?.trim(),
    //       };
    //     } else if (ele === "image") {
    //       payload = {
    //         ...payload,
    //         [ele]: values[ele],
    //       };
    //     }
    //   });
    // Append the file to FormData
    const uid = getCookie("UID");
    // console.log(imageFile, previewImage);
    if (uid) {
      payload = {
        ...payload,
        uid,
      };
    }
    if (imageFile && !isBase64Image(previewImage)) {
      payload = {
        ...payload,
        image: imageFile,
      };
    } else if (imageFile) {
      const formData = new FormData();
      formData.append("file", imageFile);
      // console.log("here");
      try {
        const res = await updateImageToURL(formData);
        // console.log(res);
        payload = {
          ...payload,
          image: res?.data?.[0]?.link,
        };
      } catch (error) {
        console.dir(error);
        // safeToast.error("Something went wrong")
        safeToast.error(error?.message);
        return;
      }
    }
    console.log(payload);
    api.sendRequest(
      completeUserProfile,
      (res) => {
        setIsLoading(false);
        safeToast.success("Profile updated Successfully.");
        authStorage?.setAuthDetails(res?.token);
        eraseCookie("UID");
        if (typeof window !== "undefined")
          localStorage.removeItem("isLinkedinState");

        router.push("/");
      },
      payload,
      "",
      (err) => {
        setIsLoading(false);
        safeToast.error(err?.message);
        // authStorage.deleteAuthDetails();
      }
    );
  };

  // const validationSchema = Yup.object().shape({
  //   firstName: Yup.string().trim().required("First Name is required"),
  //   lastName: Yup.string().trim().required("Last Name is required"),
  // });
  const validationSchema = Yup.object().shape({
    firstName: Yup.string()
      .trim()
      .required("First Name is required")
      .matches(
        /^(?![\d\W]+$)[a-zA-Z\d\W]+$/,
        "First Name cannot contain only numbers or special characters"
      ),
    lastName: Yup.string()
      .trim()
      .required("Last Name is required")
      .matches(
        /^(?![\d\W]+$)[a-zA-Z\d\W]+$/,
        "Last Name cannot contain only numbers or special characters"
      ),
    ...(isLinkedinState && {
      email: Yup.string()
        .trim()
        .required("Email is required")
        .email("Enter a valid email address"),
    }),
  });

  const formik = useFormik({
    enableReinitialize: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      handleFormSubmit(values);
    },
  });

  // Image OnChange Preview Image
  const handleProfileImage = (e) => {
    if (!e.target.files[0] || !e.target.files[0].type.startsWith("image/")) {
      safeToast.error("Unsupported file format. Please select an image file.");
      return;
    }

    setImageFile(e.target.files[0]);

    if (e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        setPreviewImage(reader.result);
      };
      reader?.readAsDataURL(e.target.files[0]);
    }
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const linkedinFlag = localStorage.getItem("isLinkedinState");
      setIsLinkedinState(linkedinFlag === "true");
    }
    const userDetails = sessionStorage.getItem("user")
      ? JSON.parse(sessionStorage.getItem("user"))
      : null;
    setUserData(userDetails);
    if (userDetails) {
      formik.setFieldValue("firstName", userDetails?.firstName);
      formik.setFieldValue("lastName", userDetails?.lastName);
      if (userDetails?.email) formik.setFieldValue("email", userDetails?.email);
      if (userDetails?.image) {
        setPreviewImage(userDetails?.image);
        setImageFile(userDetails?.image);
      }
    }
  }, []);
  return (
    <>
      <div className="tw-bg-white tw-min-h-screen">
        <CustomContainer className="tw-py-5">
          <div className="tw-flex tw-justify-start tw-h-full">
            {/* <div className="tw-relative tw-w-[100px] tw-h-[40px]  tw-items-center md:tw-gap-x-5 tw-gap-x-[0.938rem]">
              <Image src={logo} alt="logo" fill className="" />
            </div> */}
            <WebAppLogo className="tw-hidden lg:tw-block" />
          </div>
          <div>
            <CustomContainer className="tw-py-8">
              <CustomTitle
                name="Fill out your personal details to continue."
                className="tw-mx-auto tw-max-w-[20rem] tw-text-2xl tw-text-primary-black tw-font-bold tw-text-center tw-items-center tw-w-full"
                htag={1}
                textClassName="lg:!tw-text-incenti-24 !tw-text-3xl"
              />
            </CustomContainer>
            <div className="tw-flex tw-justify-center tw-items-center">
              <form
                onSubmit={formik.handleSubmit}
                className="tw-w-full md:tw-w-[70%] lg:tw-w-[37%]"
              >
                {/* Profile Picture */}
                <div className="tw-flex tw-flex-col tw-items-center tw-mb-4">
                  {previewImage ? (
                    <div className="tw-relative tw-rounded-full tw-h-36 tw-w-36">
                      <Image
                        width={144}
                        height={144}
                        src={previewImage}
                        alt="Profile"
                        className="tw-rounded-full tw-h-36 tw-w-36 tw-object-cover"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const fileInput = document.getElementById("image");
                          if (fileInput) fileInput.click();
                        }}
                        className="tw-absolute tw-bottom-2 tw-right-2 tw-bg-incenti-purple tw-text-white tw-rounded-full tw-w-8 tw-h-8 tw-flex tw-items-center tw-justify-center tw-shadow-md"
                      >
                        <EditPencileIcon width={16} height={16} />
                      </button>
                    </div>
                  ) : (
                    <label
                      htmlFor={"image"}
                      className="tw-flex tw-justify-center tw-items-center tw-rounded-full tw-h-36 tw-w-36 tw-cursor-pointer tw-bg-[#F6EFFE] tw-p-4 tw-text-center tw-border-[1.17px] tw-border-dashed tw-border-[#6D11D2]"
                    >
                      <div className="tw-flex tw-items-center tw-flex-col tw-gap-1">
                        <UserProfileIcon />
                        <span className="tw-text-gray-600 tw-text-sm">
                          Upload
                        </span>
                      </div>
                    </label>
                  )}
                  <input
                    type="file"
                    id={"image"}
                    name={"image"}
                    onChange={handleProfileImage}
                    className="tw-hidden"
                    accept="image/*"
                  />
                </div>
                {/* First Name */}
                <FloatingLabelInput
                  label="First Name*"
                  name="firstName"
                  formik={formik}
                />
                {formik.touched.firstName && formik.errors.firstName && (
                  <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                    {formik.errors.firstName}
                  </p>
                )}
                {/* Last Name */}
                <FloatingLabelInput
                  label="Last Name*"
                  name="lastName"
                  formik={formik}
                />
                {formik.touched.lastName && formik.errors.lastName && (
                  <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                    {formik.errors.lastName}
                  </p>
                )}
                {/* Last Name */}
                {isLinkedinState && (
                  <>
                    <FloatingLabelInput
                      label="Email*"
                      name="email"
                      formik={formik}
                      // disabled={formik.values.email}
                      disabled={!!userData?.email}
                    />
                    {formik.touched.email && formik.errors.email && (
                      <p className="tw-text-red-500 tw-text-sm -tw-mt-2 tw-mb-2">
                        {formik.errors.email}
                      </p>
                    )}
                  </>
                )}

                {/* About (Textarea) */}
                <div
                  onClick={() => {
                    setIsAddMore((prev) => !prev);
                  }}
                  className="tw-mb-2 tw-cursor-pointer tw-flex tw-justify-between tw-items-center"
                >
                  <p className="tw-text-primary-black tw-text-lg">
                    Add More (Optional)
                  </p>
                  {!isAddMore ? <ChevronDown /> : <ChevronUp />}
                </div>

                <div
                  className={`tw-overflow-hidden tw-transition-all tw-duration-300 ${
                    isAddMore
                      ? "tw-max-h-[900px] tw-opacity-100"
                      : "tw-max-h-0 tw-opacity-0"
                  }`}
                >
                  {/* Location */}
                  <div className="tw-mb-4">
                    <div className="tw-relative  tw-bg-[#F1F2F3] tw-pt-5 tw-pb-3 tw-px-5 tw-rounded-2xl">
                      {/* Floating Label */}
                      <label
                        htmlFor="location"
                        className={`tw-absolute tw-left-5 tw-text-primary-black tw-transition-all tw-duration-300 tw-pointer-events-none
          ${
            isFocused || location
              ? "tw-top-2 tw-text-xs tw-text-gray-500"
              : "tw-top-6 tw-text-lg"
          }`}
                      >
                        Location
                      </label>

                      <GooglePlacesAutocomplete
                        apiKey={process.env.NEXT_PUBLIC_GOOGLE_API_KEY}
                        selectProps={{
                          value: location,
                          onChange: (value) => setLocation(value),
                          onFocus: () => setIsFocused(true),
                          onBlur: () => setIsFocused(!!location),
                          placeholder: " ", // keep it blank for floating label trick
                          isClearable: true,
                          name: "location",
                          styles: {
                            control: (base, state) => ({
                              ...base,
                              backgroundColor: "transparent",
                              border: "none",
                              boxShadow: "none",
                              padding: "0",
                              minHeight: "2.5rem",
                              color: "#2D394A",
                              fontSize: "1.125rem", // Tailwind's text-lg
                              fontWeight: 600,
                            }),
                            singleValue: (base) => ({
                              ...base,
                              color: "#2D394A",
                            }),
                            placeholder: (base) => ({
                              ...base,
                              // color: "#BFBFC7",
                            }),
                            indicatorSeparator: () => ({}),
                            dropdownIndicator: () => ({
                              display: "none",
                            }),
                            menu: (base) => ({
                              ...base,
                              zIndex: 100,
                            }),
                          },
                        }}
                      />
                    </div>
                    {/* <p className="tw-italic tw-text-[#787E89] tw-font-light tw-text-sm tw-text-center">
                      Add a location to find specific project easily.
                    </p> */}
                  </div>

                  {/* Description */}
                  <FloatingLabelTextArea
                    label="About"
                    name="about"
                    formik={formik}
                  />
                  <div className="tw-flex tw-justify-end tw-mb-4">
                    <p className="tw-text-sm tw-text-[#787E89] ">
                      {formik.values.about?.length ?? 0}/250
                    </p>
                  </div>
                </div>

                {/* Submit Button */}

                <div className="tw-flex tw-mt-5 lg:tw-mt-2.5 tw-justify-center">
                  <CustomButton
                    loading={isLoading}
                    className={"!tw-px-9 !tw-py-[14px]"}
                    type="submit"
                    count={8}
                    onClick={() => {}}
                  >
                    <span className={"!tw-text-base"}>Continue</span>
                  </CustomButton>
                </div>
              </form>
            </div>
          </div>
          <div>
            <p
              className={`tw-text-sm tw-font-semibold tw-text-primary-black tw-mt-4 tw-text-center py-4 ${
                isLoading && "tw-cursor-not-allowed"
              }`}
            >
              Back to&nbsp;
              <Link
                onClick={() => {
                  authStorage.deleteAuthDetails();
                }}
                href={"/signup"}
                className="tw-text-primary-purple !tw-font-bold hover:tw-underline tw-cursor-pointer"
              >
                Sign Up
              </Link>
            </p>
          </div>
        </CustomContainer>
      </div>
    </>
  );
};

export default CreateProfile;
