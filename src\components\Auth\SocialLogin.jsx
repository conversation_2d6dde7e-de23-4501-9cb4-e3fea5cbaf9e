"use client";
import React, { useContext, useEffect, useState } from "react";
import { auth } from "@/firebase/firebase-config";
import {
  GoogleAuthProvider,
  OAuthProvider,
  signInWithPopup,
} from "firebase/auth";
import { postSocialLogin } from "@/app/action";
import toast from "react-hot-toast";
// import { useRouter } from "next/navigation";
import { useRouter } from "nextjs-toploader/app";
import authStorage from "@/utils/API/AuthStorage";
import { AppleIcon, GoogleIcon, LinkedInIcon } from "@/utils/icons";
import { valContext } from "../context/ValContext";
import useApiRequest from "../helper/hook/useApiRequest";
import FullScreenLoader from "../Loader/FullScreenLoader";
import { getOS, LINKEDIN_REDIRECT_URL, OS_TYPE } from "@/utils/function";
import setCookie from "@/utils/Cookies";
import { useLinkedIn } from "react-linkedin-login-oauth2";
import { safeToast } from "@/utils/safeToast";

const SocialLogin = ({ onClick = () => {}, isRegister = false }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isMacOs, setIsMacOs] = useState(false);
  const {
    isGoogleDisabled,
    setIsGoogleDisabled,
    isAppleDisabled,
    setIsAppleDisabled,
  } = useContext(valContext);
  const api = useApiRequest();
  // auth.useDeviceLanguage();
  const handleSocialLogin = async (providerType) => {
    setIsLoading(true);
    let provider;
    if (providerType === "google") {
      provider = new GoogleAuthProvider();
      provider.addScope("email");
    } else if (providerType === "apple") {
      provider = new OAuthProvider("apple.com");
      provider.addScope("email");
      provider.addScope("name");
    }

    try {
      const result = await signInWithPopup(auth, provider);
      // console.log(result, "result");
      // debugger
      if (result && result.user) {
        const payload = { firebaseToken: result.user.accessToken };

        if (isRegister) {
          onClick(result.user);
          if (providerType === "google") setIsGoogleDisabled(true);
          if (providerType === "apple") setIsAppleDisabled(true);
        } else {
          api.sendRequest(
            postSocialLogin,
            (res) => {
              authStorage.setAuthDetails(res?.token);
              authStorage.setProfileDetails(res.data);
              setCookie("UID", res?.data?.uid, 365);

              if (res?.data?.isNewUser) {
                router.push("/create-profile");
                safeToast.success(
                  "Your email is verified. Pleas complete your registration."
                );
                sessionStorage.setItem("user", JSON.stringify(res.data));
              } else {
                router.push("/");
              }
            },
            payload
          );
        }
      }
    } catch (error) {
      console.log(error);
      safeToast.error(`${providerType} Login Error`, error);
      setIsLoading(false);
    }
  };

  // Linkdin Login

  const linkedInLogin = () => {
    const clientId = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;
    const redirectUri = LINKEDIN_REDIRECT_URL;
    const scope = process.env.NEXT_PUBLIC_LINKEDIN_SCOPE;
    const state = crypto.randomUUID();
    localStorage.setItem("isLinkedinState", true);
    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(
      redirectUri
    )}&scope=${encodeURIComponent(scope)}&state=${state}`;

    window.location.href = authUrl;
  };

  useEffect(() => {
    setIsMacOs(getOS() === OS_TYPE.MACOS);
    localStorage.removeItem("isLinkedinState");
  }, []);
  return (
    <>
      {isLoading && <FullScreenLoader />}
      <div className="tw-flex tw-items-center tw-justify-center tw-gap-6 tw-px-5 tw-w-full">
        <div className="tw-flex tw-flex-col tw-gap-1 tw-items-center">
          <button
            className={`${
              isGoogleDisabled ? "tw-bg-[#fafafa] tw-cursor-not-allowed" : ""
            } tw-bg-gray-200 tw-p-4 tw-rounded-full tw-flex tw-items-center tw-justify-center  hover:tw-bg-gray-100 hover:tw-shadow-xl tw-transition tw-duration-300`}
            onClick={() => handleSocialLogin("google")}
          >
            <GoogleIcon width={22} height={22} />
          </button>
          <p className="tw-text-primary-black tw-text-sm">Google</p>
        </div>
        <div className="tw-flex tw-flex-col tw-gap-1 tw-items-center">
          <button
            className="tw-bg-gray-200 tw-p-4 tw-rounded-full tw-flex tw-items-center tw-justify-center  hover:tw-bg-gray-100 hover:tw-shadow-xl tw-transition tw-duration-300"
            onClick={linkedInLogin}
          >
            <LinkedInIcon width={22} height={22} />
          </button>
          <p className="tw-text-primary-black tw-text-sm">LinkedIn</p>
        </div>
        {isMacOs && (
          <div className="tw-flex tw-flex-col tw-gap-1 tw-items-center">
            <button
              className={`${
                isAppleDisabled ? "tw-bg-[#fafafa] tw-cursor-not-allowed" : ""
              } tw-bg-gray-200 tw-p-4 tw-rounded-full tw-flex tw-items-center tw-justify-center  hover:tw-bg-gray-100 hover:tw-shadow-xl tw-transition tw-duration-300`}
              onClick={() => handleSocialLogin("apple")}
            >
              <AppleIcon width={22} height={22} />
            </button>
            <p className="tw-text-primary-black tw-text-sm">Apple</p>
          </div>
        )}
      </div>
    </>
  );
};

export default SocialLogin;
