"use client";
import Image from "next/image";
import logo from "../../../public/images/logo/logo.png";
import { useEffect, useState } from "react";
import Marquee from "react-fast-marquee";

const LoginFormLeftLayout = () => {
  const [changeDirection, setChangeDirection] = useState("left");
  const [index, setIndex] = useState(0);
  const [reverse, setReverse] = useState(false);

  const words = ["Creator", "Communities", "Projects"];

  useEffect(() => {
    const interval = setInterval(() => {
      setChangeDirection((prev) => (prev === "left" ? "right" : "left"));
    }, 10000);

    return () => clearInterval(interval); // Cleanup interval on unmount
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => {
        if (!reverse) {
          if (prev === words.length - 1) {
            setReverse(true); // Start reversing after 3 words
            return prev - 1;
          }
          return prev + 1;
        } else {
          if (prev === 0) {
            setReverse(false); // Start moving up again after reversing
            return prev + 1;
          }
          return prev - 1;
        }
      });
    }, 2000); // Change every 1.5 seconds

    return () => clearInterval(interval);
  }, [reverse]);
  return (
    <>
      <div className="tw-hidden lg:tw-block tw-min-h-screen tw-relative tw-z-30 tw-bg-primary-black">
        <div className="image-shadow tw-min-w-full tw-min-h-screen !tw-absolute !tw-z-20"></div>

        <Marquee
          speed={100}
          direction={changeDirection}
          className="tw-min-h-screen !tw-absolute !tw-z-10"
        >
          <div className="auth-left-side tw-min-h-screen tw-w-[800px] tw-me-[10px]" />
          <div className="tw-me-[10px] auth-left-side tw-min-h-screen tw-w-[800px]" />
        </Marquee>

        <div className="tw-absolute tw-inset-0 tw-flex tw-flex-col tw-justify-end tw-pb-9 2xl:tw-pb-14 tw-mx-2 2xl:tw-px-10 xl:tw-px-8 tw-z-20  tw-gap-4">
          <div className="tw-relative ">
            <Image src={logo} width={125} alt="logo" />
          </div>

          <h1 className="tw-max-w-[35rem] 2xl:tw-max-w-[46rem] tw-text-[45px] md:tw-text-[45px] xl:tw-text-[3.6rem]  2xl:tw-text-[4.6rem] tw-text-white tw-font-extrabold tw-font-inter tw-leading-full 2xl:tw-leading-[110%]">
            <span className="tw-line-clamp-2">
              Organize your social media &
            </span>
            <span className="tw-relative tw-inline-block tw-w-full tw-overflow-hidden tw-h-[60px] md:tw-h-[60px] xl:tw-h-[70px] tw-align-middle">
              Find
              {words.map((word, i) => (
                <span
                  key={i}
                  className={`tw-absolute tw-text-primary-purple tw-w-full tw-transition-transform tw-duration-500 tw-ease-in-out ${
                    i === index
                      ? "tw-translate-y-0"
                      : i > index
                      ? "tw-translate-y-full"
                      : "tw--translate-y-full"
                  }`}
                >
                  &nbsp;{word}
                </span>
              ))}
            </span>
          </h1>
          <p className="tw-text-white tw-text-[1.25rem]  tw-max-w-[25rem] tw-font-inter">
            Inspire Others by Unleashing your Own Growth Potential
          </p>
        </div>
      </div>
    </>
  );
};

export default LoginFormLeftLayout;
