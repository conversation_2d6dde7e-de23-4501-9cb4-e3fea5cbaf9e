"use client";

import { useState } from "react";
import dynamic from "next/dynamic";
import Image from "next/image";
import "plyr-react/plyr.css";

// Dynamically import Plyr only when needed
const Plyr = dynamic(() => import("plyr-react"), { ssr: false });

function getYouTubeThumbnail(url, quality = "hqdefault") {
  try {
    let videoId = "";
    if (url.includes("youtu.be")) {
      videoId = url.split("/").pop();
    } else if (url.includes("youtube.com")) {
      videoId = new URL(url).searchParams.get("v");
    }
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
  } catch {
    return null;
  }
}

export default function VideoPlayer({ url = "" }) {
  const [isPlaying, setIsPlaying] = useState(false);

  const getSourceConfig = () => {
    if (url.includes("youtube.com") || url.includes("youtu.be")) {
      const videoId = url.includes("youtu.be")
        ? url.split("/").pop()
        : new URL(url).searchParams.get("v");
      return {
        type: "video",
        sources: [{ src: videoId, provider: "youtube" }],
      };
    }
    return {
      type: "video",
      sources: [{ src: url, type: "video/mp4" }],
    };
  };

  const thumbnail =
    url.includes("youtube") || url.includes("youtu.be")
      ? getYouTubeThumbnail(url, "maxresdefault")
      : null;

  return (
    <div className="tw-relative tw-w-full tw-h-full tw-aspect-square tw-bg-black tw-rounded-[1.5rem] tw-overflow-hidden">
      {!isPlaying && thumbnail ? (
        <button
          onClick={() => setIsPlaying(true)}
          className="tw-w-full tw-h-full tw-relative"
        >
          <Image
            src={thumbnail}
            alt="Video thumbnail"
            fill
            className="tw-object-cover"
          />
          <div className="tw-absolute tw-inset-0 tw-flex tw-items-center tw-justify-center tw-bg-black/40">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="tw-h-16 tw-w-16 tw-text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>
        </button>
      ) : (
        <Plyr
          source={getSourceConfig()}
          options={{
            autoplay: true,
            controls: [
              "play",
              "progress",
              // "current-time",
              // "mute",
              // "volume",
              // "settings",
              // "fullscreen",
            ],
          }}
        />
      )}
    </div>
  );
}
