import Modal from "react-responsive-modal";
import "react-responsive-modal/styles.css";
import Stories from "react-insta-stories";
import { MoreVertical, Pencil, PenLine, Trash2 } from "lucide-react";
import { CloseModalIcon } from "@/utils/icons";
import VideoPlayer from "./VideoPlayer/VideoPlayer";
import HLSPlayer from "./VideoPlayer/HLSPlayer";
import dayjs from "dayjs";
import {
  createProxyImageUrl,
  fonts,
  generateAvatarCanvas,
  getTimePassedFromNow,
  parseColor,
} from "@/utils/function";
import { fontMap } from "./Font";
import NextImage from "next/image";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "nextjs-toploader/app";

import { useEffect } from "react";
import { useState } from "react";
import PopUpModal from "../Common/PopUpModal";
import useApiRequest from "../helper/hook/useApiRequest";
import { deleteHighlights, updateHighlights } from "@/app/action";
import { safeToast } from "@/utils/safeToast";
import AddHighlightModal from "../Profile/AddHighlightModal";
import AnimatedAlignPositioned from "./AnimatedAlignPositioned";

// Function to generate canvas-based avatar

// Custom Header Component with 3-dot settings
const CustomStoryHeader = ({
  profileImage,
  heading,
  subheading,
  currentUser,
  onAction,
  ele,
  action,
  setCurrentAction,
  isSettingVisible = false,
}) => {
  const settings = [
    {
      label: "Edit Highlight",
      icon: <Pencil stroke="#2D394A" size={17} />,
      onClick: () => {
        action("pause");
        setCurrentAction(action);
        onAction("edit", currentUser);
      },
    },
    {
      label: "Remove Story",
      className: "tw-text-[#EF3B41] hover:tw-text-[#EF3B41]",
      icon: <Trash2 stroke="#EF3B41" size={17} />,
      onClick: (ele) => {
        // console.log(ele, "here");
        onAction("delete", ele);
      },
    },
  ];

  // const handleSettingsAction = (action, event) => {
  //   event.stopPropagation();
  //   if (onAction) {
  //     onAction(action, currentUser);
  //   }
  // };

  return (
    <div className="tw-absolute tw-top-2 tw-left-0 tw-w-full tw-flex tw-items-center tw-gap-2.5 tw-p-4 tw-z-[9999] ">
      <div className="tw-relative tw-w-12 tw-h-12 tw-rounded-full">
        <NextImage
          src={profileImage}
          alt="User"
          className="!tw-rounded-full !tw-object-cover tw-border-2 tw-border-white"
          fill
        />
      </div>
      <div className="tw-flex-1">
        <p className="tw-text-[#ffffffe6] tw-font-medium">{heading}</p>
        <p className="tw-text-[#fffc] tw-text-[.6rem]">{subheading}</p>
      </div>

      {isSettingVisible && (
        <DropdownMenu
          onOpenChange={(open) => {
            if (open) {
              // Pause story when dropdown opens
              setCurrentAction(action);
              action("pause");
              // console.log("Dropdown opened - story paused");
            } else {
              // Resume story when dropdown closes (optional)
              // console.log("Dropdown clo/sed");
            }
          }}
        >
          <DropdownMenuTrigger asChild>
            <div
              onClick={(e) => {
                e.stopPropagation();
                // console.log("Dropdown trigger clicked");
              }}
              className="tw-text-white tw-p-2 hover:tw-bg-white/20 tw-rounded-full tw-transition-colors tw-cursor-pointer"
            >
              <MoreVertical size={20} />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="tw-z-[99999] tw-bg-white tw-border tw-border-gray-200 tw-rounded-md tw-shadow-lg tw-min-w-[160px]"
            align="end"
            side="bottom"
            sideOffset={8}
          >
            {settings.map((setting, index) => (
              <DropdownMenuItem
                key={index}
                className={`tw-flex tw-items-center tw-gap-3 tw-px-3 tw-py-2.5 tw-cursor-pointer tw-text-sm hover:tw-bg-gray-50 focus:tw-bg-gray-50 tw-transition-colors ${
                  setting.className || ""
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  setting.onClick(ele);
                }}
              >
                <span className="tw-flex tw-items-center tw-justify-center tw-w-4 tw-h-4">
                  {setting.icon}
                </span>
                <span className="tw-flex-1">{setting.label}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};

const StoriesPreview = ({
  isOpen,
  setIsOpen,
  currentUserIndex,
  setCurrentUserIndex,
  storyData,
  dataKey = "Stories",
  setReload = () => {},
  isSettingVisible = false,
  isHighlight = false,
}) => {
  const router = useRouter();
  const [deleteData, setDeleteData] = useState(null);
  const [isPaused, setIsPaused] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);
  const [stories, setStories] = useState([]);
  const [isHighlighModalOpen, setIsHighlighModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const api = useApiRequest(false);

  // console.log(deleteData, "deleteData");
  // Handler for settings actions
  const handleHeaderAction = (type, data) => {
    setIsPaused(true);
    switch (type) {
      case "delete":
        // console.log("Muting user:", data);
        setDeleteData({
          ...data,
        });
        break;
      case "edit":
        setIsHighlighModalOpen(true);
        setEditData(data);
        break;

      default:
      // console.log("Unknown action:", type);
    }
  };

  // Preload images for faster rendering
  useEffect(() => {
    if (!storyData || storyData.length === 0) return;

    const preloadImages = () => {
      // Preload current user's images
      const currentUser = storyData[currentUserIndex];
      currentUser[dataKey]?.forEach((story) => {
        if (story?.Story?.overlayImage ?? story?.overlayImage) {
          const img = new Image();
          img.src = story?.Story?.overlayImage ?? story?.overlayImage;
        }
        if (
          (story?.Story?.mediaLink && story?.Story?.mediaType === "image") ||
          (story?.mediaLink && story?.mediaType === "image")
        ) {
          const img = new Image();
          img.src = story?.Story?.mediaLink ?? story.mediaLink;
        }
      });

      // Preload next user's first image
      if (currentUserIndex < storyData.length - 1) {
        const nextUser = storyData[currentUserIndex + 1];
        const firstStory = nextUser?.Stories?.[0];
        if (firstStory?.overlayImage) {
          const img = new Image();
          img.src = firstStory.overlayImage;
        }
      }

      // Preload previous user's last image
      if (currentUserIndex > 0) {
        const prevUser = storyData[currentUserIndex - 1];
        const lastStory = prevUser?.Stories?.[prevUser.Stories.length - 1];
        if (lastStory?.overlayImage) {
          const img = new Image();
          img.src = lastStory.overlayImage;
        }
      }
    };

    preloadImages();
  }, [currentUserIndex, storyData]);

  const navigate = (direction) => {
    if (direction === "next") {
      // Go to next user
      if (currentUserIndex < storyData.length - 1) {
        setCurrentUserIndex((prev) => prev + 1);
      }
    } else if (direction === "prev") {
      // Go to previous user
      if (currentUserIndex > 0) {
        setCurrentUserIndex((prev) => prev - 1);
      }
    }
  };

  // console.log(deleteData);
  const renderStoryContent = (story, header, currentUser) => {
    const { overlayImage, properties = [] } = story?.Story ?? story;

    return ({ action }) => (
      <div className="tw-w-full tw-h-full">
        {header && (
          <CustomStoryHeader
            profileImage={header?.profileImage}
            heading={header?.heading}
            subheading={header?.subheading}
            currentUser={currentUser}
            onAction={handleHeaderAction}
            ele={{
              id: currentUser?.id,
              storyId: story?.Story?.id,
            }}
            action={action}
            setCurrentAction={setCurrentAction}
            isSettingVisible={isSettingVisible}
          />
        )}
        <div
          style={{
            width: "100%",
            height: "100%",
            position: "relative",
          }}
        >
          {/* Use Next.js Image for faster loading */}
          {overlayImage ? (
            <NextImage
              src={overlayImage}
              alt="Story background"
              fill
              className="tw-object-contain"
              priority
              sizes="100vw"
              quality={85}
              onError={(e) => {
                // console.error("Failed to load overlay image:", overlayImage);
                // Fallback to background image if NextImage fails
                e.target.style.display = "none";
                e.target.parentElement.style.backgroundImage = `url(${overlayImage})`;
                e.target.parentElement.style.backgroundSize = "cover";
                e.target.parentElement.style.backgroundPosition = "center";
              }}
            />
          ) : (
            <div
              style={{
                width: "100%",
                height: "100%",
                backgroundColor: "#f0f0f0",
              }}
            />
          )}
          {/* Render all properties */}
          {properties.map((prop, index) => {
            const dx = prop?.position?.dx ?? 0;
            const dy = prop?.position?.dy ?? 0;
            const backGroundColor = prop?.backGroundColor ?? "transparent";
            const text = prop?.text ?? "";
            const fontSize = prop?.fontSize ?? 16;
            const textColor = prop?.textColor ?? "#000";
            const scale = prop?.scale ?? 1;
            const rotation = prop?.rotation ?? 0;
            const fontFamily = prop?.fontFamily ?? "default";
            const slug = prop?.slug;
            const textAlignPosition = {
              0: "center",
              1: "left",
              2: "right",
            };

            const selectedFont = fonts[fontFamily];
            const fontClass = fontMap[selectedFont]?.className ?? "";

            return (
              <div className="tw-w-full tw-h-full tw-absolute " key={index}>
                <AnimatedAlignPositioned
                  dx={dx}
                  dy={dy}
                  alignment={textAlignPosition[0]}
                  rotateDegrees={rotation}
                  duration={0}
                  curve="easeInOut"
                >
                  <Popover
                    key={index}
                    onOpenChange={(open) => {
                      if (open) {
                        action("pause");
                      } else {
                        action("play");
                      }
                    }}
                  >
                    <PopoverTrigger asChild>
                      <div
                        className={fontClass}
                        style={{
                          // position: "absolute",
                          // top: `calc(55% + ${dy * 50}%)`, // convert -1..1 to percent offset from center
                          // left: `calc(55% + ${dx * 50}%)`,
                          // transform: `translate(-50%, -50%) scale(${scale}) rotate(${rotation}rad)`,
                          transform: `scale(${scale}) rotate(${rotation}rad)`,
                          color: parseColor(textColor),
                          fontSize: `${fontSize - 5}px`,
                          fontWeight: "500",
                          padding: "0.6rem",
                          borderRadius: "10px",
                          backgroundColor: parseColor(backGroundColor),
                          cursor: "pointer",
                          pointerEvents: "auto",
                          zIndex: 999999 + index, // Ensure each element has unique z-index
                          // maxWidth: "80%",
                          wordWrap: "break-word",
                          // width: "100%",
                          textAlign: textAlignPosition[prop?.textAlign ?? 0],
                        }}
                      >
                        {text}
                      </div>
                    </PopoverTrigger>
                    <PopoverContent
                      side="top"
                      align="center"
                      className="tw-max-w-xs tw-p-4 tw-text-center "
                    >
                      <p
                        onClick={() => {
                          if (slug) {
                            router.push(`/projects/${slug}`);
                          }
                        }}
                      >
                        {text}
                      </p>
                    </PopoverContent>
                  </Popover>
                </AnimatedAlignPositioned>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const currentUser = storyData[currentUserIndex];

  // console.log(currentUser);

  // Function to create stories from current user data
  const createStoriesFromData = (currentUser, dataKey) => {
    return currentUser?.[dataKey]
      ?.map((story) => {
        const {
          mediaLink,
          mediaType,
          duration = 5,
          overlayImage,
        } = story?.Story ?? story;
        // Safety check for required story data
        if (!story || (!mediaLink && !overlayImage)) {
          return null;
        }
        const userName = `${currentUser?.title ?? currentUser?.firstName} ${
          currentUser?.lastName ?? ""
        }`;
        const header = {
          heading: userName,
          profileImage: currentUser.image
            ? createProxyImageUrl(currentUser.image)
            : generateAvatarCanvas(userName, 100),
          subheading: getTimePassedFromNow(
            isHighlight
              ? currentUser?.createdAt
              : story?.Story?.createdAt ?? story?.createdAt
          ),
        };

        const storyId = story?.Story?.id ?? story?.id;

        if (mediaType === "text") {
          return {
            content: renderStoryContent(story, header, currentUser),
            storyId,
          };
        } else if (mediaType === "video") {
          if (mediaLink.endsWith(".m3u8")) {
            return {
              content: ({ action }) => (
                <HLSPlayer
                  overlayImage={overlayImage}
                  url={mediaLink}
                  action={action}
                  header={header}
                  customHeader={
                    <CustomStoryHeader
                      profileImage={header?.profileImage}
                      heading={header?.heading}
                      subheading={header?.subheading}
                      currentUser={currentUser}
                      onAction={handleHeaderAction}
                      ele={{
                        id: currentUser?.id,
                        storyId,
                      }}
                      action={action}
                      setCurrentAction={setCurrentAction}
                      isSettingVisible={isSettingVisible}
                    />
                  }
                />
              ),
              duration: duration * 1000,
              header,
              storyId,
            };
          }

          // For mp4/webm
          return {
            content: ({ action }) => (
              <VideoPlayer
                overlayImage={overlayImage}
                url={mediaLink}
                action={action}
                header={header}
                customHeader={
                  <CustomStoryHeader
                    profileImage={header?.profileImage}
                    heading={header?.heading}
                    subheading={header?.subheading}
                    currentUser={currentUser}
                    onAction={handleHeaderAction}
                    ele={{
                      id: currentUser?.id,
                      storyId,
                    }}
                    action={action}
                    setCurrentAction={setCurrentAction}
                    isSettingVisible={isSettingVisible}
                  />
                }
              />
            ),
            duration: duration * 1000,
            header,
            storyId,
          };
        }

        // Fallback for images - use content property for consistency
        return {
          content: ({ action }) => (
            <div className="tw-w-full tw-h-full">
              {header && (
                <CustomStoryHeader
                  profileImage={header?.profileImage}
                  heading={header?.heading}
                  subheading={header?.subheading}
                  currentUser={currentUser}
                  onAction={handleHeaderAction}
                  ele={{
                    id: currentUser?.id,
                    storyId,
                  }}
                  action={action}
                  setCurrentAction={setCurrentAction}
                  isSettingVisible={isSettingVisible}
                />
              )}
              {overlayImage || mediaLink ? (
                <NextImage
                  src={overlayImage || mediaLink}
                  alt="Story image"
                  fill
                  className="tw-object-cover"
                  priority
                  sizes="100vw"
                  quality={85}
                  onError={(e) => {
                    console.error(
                      "Failed to load story image:",
                      overlayImage || mediaLink
                    );
                    // Fallback to background image if NextImage fails
                    e.target.style.display = "none";
                    e.target.parentElement.style.backgroundImage = `url(${
                      overlayImage || mediaLink
                    })`;
                    e.target.parentElement.style.backgroundSize = "cover";
                    e.target.parentElement.style.backgroundPosition = "center";
                  }}
                />
              ) : (
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                    backgroundColor: "#f0f0f0",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "#666",
                  }}
                >
                  No Image Available
                </div>
              )}
            </div>
          ),
          header,
          storyId,
        };
      })
      .filter(Boolean); // Remove any null/undefined stories
  };

  // Update stories when currentUser or dataKey changes
  useEffect(() => {
    const newStories = createStoriesFromData(currentUser, dataKey);
    setStories(newStories);
  }, [currentUser, dataKey]);

  return (
    <div>
      <AddHighlightModal
        isOpen={isHighlighModalOpen}
        setIsOpen={setIsHighlighModalOpen}
        setRefresh={setReload}
        editData={editData}
      />
      <Modal
        open={isOpen}
        onClose={() => {
          setIsOpen(false);
        }}
        center
        classNames={{
          modal:
            "!tw-p-0 !tw-m-0 !tw-bg-black !tw-text-white !tw-w-full !tw-h-screen",
          closeButton: "!tw-z-[99999]",
        }}
        //   closeIcon={<X className="tw-text-white" />}
        closeIcon={
          <div className="tw-relative tw-top-[5rem] tw-right-[1.5rem] md:tw-static ">
            <CloseModalIcon size={30} />
          </div>
        }
        styles={{ modal: { maxWidth: "100vw", width: "100%", padding: 0 } }}
        showCloseIcon={true}
        focusTrapped={false}
      >
        <PopUpModal
          isLoading={api.isLoading}
          contentClassName="!tw-z-[99999]"
          isOpen={deleteData}
          confirmButtonText={stories?.length > 1 ? "Remove" : "Delete"}
          setIsOpen={(value) => {
            setDeleteData(value);
            // Resume story when modal is closed without confirming
            if (!value && currentAction && isPaused) {
              currentAction("play");
              setIsPaused(false);
            }
          }}
          mainMessage={
            stories?.length > 1
              ? "Remove this story?"
              : "Delete This Highlight?"
          }
          subMessage={`Are you sure you want to ${
            stories?.length > 1 ? "remove this story" : "delete this highlight"
          }? Once removed , it cannot be recovered.`}
          onConfirm={() => {
            const apiCall =
              stories?.length > 1 ? updateHighlights : deleteHighlights;
            let payload =
              stories?.length > 1
                ? {
                    id: deleteData?.id,
                    RemoveStoriesIds: [deleteData?.storyId],
                  }
                : {
                    id: deleteData?.id,
                  };
            // console.log(payload);
            api.sendRequest(
              apiCall,
              (res) => {
                // console.log(res);

                if (stories?.length > 1) {
                  safeToast.success("Highlight updated successfully");
                  // Remove the deleted story from the stories state
                  setStories((prevStories) =>
                    prevStories.filter(
                      (story) => story.storyId !== deleteData?.storyId
                    )
                  );
                } else {
                  safeToast.success(res?.message);
                  setReload((prev) => !prev);
                  setIsOpen(false);
                }

                // resetState();
                setDeleteData(null);
                setIsPaused(false);
                // Don't resume story after deletion - let it continue naturally
              },
              payload,
              ""
            );
          }}
        />
        <div className="tw-relative tw-w-full tw-h-screen tw-flex tw-items-center tw-justify-center">
          <Stories
            key={`user-${currentUserIndex}`}
            stories={stories}
            defaultInterval={6000}
            storyContainerStyles={{
              overflow: "hidden",
            }}
            keyboardNavigation
            height="95vh"
            onAllStoriesEnd={() => {
              if (currentUserIndex < storyData.length - 1) {
                setCurrentUserIndex((prev) => prev + 1);
              } else {
                setIsOpen(false);
              }
            }}
          />

          {/* Navigation buttons */}
          {/* {currentUserIndex > 0 && (
            <button
              onClick={() => navigate("prev")}
              className="tw-z-[9999] tw-absolute tw-left-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-white tw-text-center hover:tw-bg-[#5d5a58] tw-transition-colors"
            >
              <ChevronLeft size={25} />
            </button>
          )}
          {currentUserIndex < storyData.length - 1 && (
            <button
              onClick={() => navigate("next")}
              className="tw-z-[9999] tw-absolute tw-right-[34.5%] tw-top-1/2 tw-rounded-full -tw-translate-y-1/2 tw-bg-[#4d4a48] tw-p-2 tw-text-center tw-text-white hover:tw-bg-[#5d5a58] tw-transition-colors"
            >
              <ChevronRight size={25} />
            </button>
          )} */}
        </div>
      </Modal>
    </div>
  );
};

export default StoriesPreview;
