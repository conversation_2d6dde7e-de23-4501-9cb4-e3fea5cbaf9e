import { handleCopy, ReplaceBackslashN } from "@/utils/function";
import { GenieChatIcon, SentChatIcon } from "@/utils/icons";
import dayjs from "dayjs";
import { Copy, X } from "lucide-react";

const GenieBot = ({
  message,
  setMessage,
  firstTimeLoading,
  chatList = [],
  setIsBotOpen,
  isMessageFetching,
  chatHandler,
  isDataFetching,
  chatContainerRef,
  role,
  textareaRef,
}) => {
  return (
    <>
      <div className="tw-relative tw-h-full">
        <div className="tw-flex  tw-justify-between tw-items-center tw-mb-2">
          <div />
          <div className="tw-flex tw-gap-2 tw-items-center">
            <GenieChatIcon size={25} /> <strong>Flowie</strong>
          </div>
          <button
            onClick={() => {
              setIsBotOpen(false);
              setMessage("");
            }}
            className=""
          >
            <X className="tw-h-7 tw-w-7" />
          </button>
        </div>
        <p className="tw-text-center tw-mr-5 tw-text-primary-black tw-text-opacity-70 tw-text-xs tw-my-2">
          {dayjs().format("MMM DD, YYYY")}
        </p>
        <div
          ref={chatContainerRef}
          className="tw-h-[63vh] lg:tw-h-[26rem] tw-overflow-auto show-scrollbar tw-pr-2"
        >
          {firstTimeLoading ? (
            <div className="tw-my-2">
              <MessageLoader />
            </div>
          ) : (
            <div className="tw-pb-2">
              {chatList?.map((ele, i) => (
                <div
                  key={`${ele.content}-${i}`}
                  className={`tw-flex ${
                    ele?.role === role?.user ? "tw-justify-end" : ""
                  }`}
                >
                  <div className="tw-flex tw-flex-col">
                    <div
                      className={`  ${
                        ele?.role === role?.user
                          ? " tw-rounded-bl-2xl tw-bg-[#F6EFFE]"
                          : "tw-rounded-br-2xl tw-bg-[#F7F7F2]"
                      } tw-my-2 tw-max-w-[18rem] tw-block tw-rounded-tl-2xl tw-rounded-tr-2xl  tw-p-2.5 ${
                        i % 10 !== 0 && "tw-break-all"
                      }`}
                    >
                      {i % 10 === 0 ? (
                        ele?.content
                      ) : (
                        <div
                          className="tw-text-primary-black tw-font-semibold"
                          dangerouslySetInnerHTML={{
                            __html: ReplaceBackslashN(ele?.content),
                          }}
                        />
                      )}
                      <p className="tw-text-xs tw-text-right tw-text-[#67737D] tw-font-light">
                        {dayjs(ele.timestamp).format("hh:mm A")}
                      </p>
                    </div>
                    <button
                      onClick={() => {
                        handleCopy(ele?.content);
                      }}
                      type="button"
                    >
                      <Copy size={15} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
          {isMessageFetching && (
            <div className="tw-my-2">
              <MessageLoader />
            </div>
          )}
        </div>

        {/* Input Message */}
        <div className="tw-absolute tw-bottom-0 tw-w-full">
          <div className="tw-flex tw-gap-2 tw-items-center">
            <textarea
              ref={textareaRef}
              type="text"
              name="content"
              value={message ?? ""}
              onFocus={(e) => {
                e.target.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });
              }}
              onInput={() => {
                if (!isDataFetching) {
                  const textarea = textareaRef.current;
                  if (textarea) {
                    textarea.style.height = "auto"; // reset to auto to shrink if needed
                    textarea.style.height = `${textarea.scrollHeight}px`; // set height to scrollHeight
                  }
                }
              }}
              onKeyDown={(event) => {
                if (event.key === "Enter" && !isDataFetching && message) {
                  event.preventDefault();
                  chatHandler();
                }
              }}
              onChange={(e) => {
                setMessage(e.target.value?.replace(/\n/g, ""));
              }}
              rows={1}
              placeholder="Message"
              className="tw-rounded-lg tw-font-normal tw-py-[0.75rem] tw-outline-none tw-ps-3 tw-pe-2 tw-w-full tw-bg-[#F1F2F3] tw-text-primary-black tw-resize-none tw-overflow-hidden tw-min-h-[2rem]"
            />
            {/* <input
              type="text"
              name="content"
              value={message ?? ""}
              onFocus={(e) => {
                e.target.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });
              }}
              onKeyDown={(event) => {
                if (event.key === "Enter" && !isDataFetching) {
                  chatHandler();
                }
              }}
              onChange={(e) => {
                setMessage(e.target.value);
              }}
              placeholder="Message"
              className="tw-rounded-lg tw-font-normal tw-py-2 tw-outline-none tw-ps-3 tw-pe-2 tw-w-full tw-bg-[#F1F2F3] tw-text-primary-black"
            /> */}
            {message && !isDataFetching && (
              <button
                onClick={chatHandler}
                type="submit"
                className="tw-cursor-pointer"
              >
                <SentChatIcon size={35} />
              </button>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default GenieBot;

function MessageLoader() {
  return (
    <div className="tw-bg-[#F7F7F2] tw-inline-block tw-rounded-tl-2xl tw-rounded-tr-2xl tw-rounded-br-2xl tw-p-2.5">
      <div className="chat-loader"></div>
    </div>
  );
}
