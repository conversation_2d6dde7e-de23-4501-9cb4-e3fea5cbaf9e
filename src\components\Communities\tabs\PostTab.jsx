"use client";
import { valContext } from "@/components/context/ValContext";
import { EditPencilIcon } from "@/utils/icons";
import { useContext, useEffect, useState } from "react";
import CommunityPost from "../CommunityPost";
import authStorage from "@/utils/API/AuthStorage";

const PostTab = ({
  origin,
  communityData,
  communityPostVisiblity = "update",
}) => {
  const [userData, setUserData] = useState({});
  const {
    communityPostPagination: pagination,
    setCommunityPostPagination: setPagination,
    communityPostList: dataList,
    setCommunityPostList: setDataList,
    loginUserData,
  } = useContext(valContext);
  const resetDataList = () => {
    setDataList([]);
    setPagination({
      page: 1,
      limit: 20,
      total: 0,
    });
  };

  useEffect(() => {
    if (loginUserData) {
      setUserData(loginUserData);
    } else {
      setUserData(authStorage.getProfileDetails());
    }
  }, []);

  useEffect(() => {
    resetDataList();
  }, []);
  return (
    <>
      <CommunityPost
        CommunityId={communityData?.id}
        userData={userData}
        userId={communityData?.UserId}
        dataList={dataList}
        setDataList={setDataList}
        pagination={pagination}
        setPagination={setPagination}
        resetDataList={resetDataList}
        origin={origin}
        communityPostVisiblity={communityPostVisiblity}
      />
    </>
  );
};

export default PostTab;
